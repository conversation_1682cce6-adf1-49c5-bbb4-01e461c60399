#!/usr/bin/env python3
"""
测试最终的终端捕获功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_final_capture():
    """测试最终的终端捕获功能"""
    print("=" * 60)
    print("测试最终的终端捕获功能")
    print("=" * 60)
    
    # 捕获的日志
    captured_logs = []
    
    def safe_log_capture(message):
        """安全的日志捕获"""
        captured_logs.append(message)
        # 使用原始stderr输出，避免被捕获
        original_stderr = sys.__stderr__
        original_stderr.write(f"[CAPTURED] {message}\n")
        original_stderr.flush()
    
    # 创建最终版本的终端捕获器
    from ui.widgets.training_widget import TerminalCapture
    terminal_capture = TerminalCapture(safe_log_capture)
    
    print("开始测试最终的终端捕获功能...")
    
    try:
        # 开始捕获
        terminal_capture.start_capture()
        
        # 测试基本输出
        print("这是第一条测试消息")
        print("这是第二条测试消息")
        
        # 测试YOLO风格的输出
        print("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
        print("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
        print("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 测试进度条输出（模拟YOLO的实际输出）
        print("      1/100      1.05G      1.523      3.141      4.765      1.325         22        640: 100%|██████████| 96/96 [00:41<00:00,  2.34it/s]")
        print("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|██████████| 3/3 [00:01<00:00,  2.86it/s]")
        print("                   all         24        196     0.0662     0.0864     0.0428     0.0302     0.0116     0.0451    0.00739    0.00122")
        
        # 测试带ANSI转义序列的输出
        print("\033[32m这是绿色文本\033[0m")
        print("\033[31m这是红色文本\033[0m")
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止捕获
        terminal_capture.stop_capture()
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("最终终端捕获测试结果:")
    print(f"捕获的日志条数: {len(captured_logs)}")
    
    # 显示捕获的日志
    print("\n捕获的日志内容:")
    for i, log in enumerate(captured_logs):
        print(f"  {i+1:2d}. {log}")
    
    # 检查关键功能
    has_basic = any('测试消息' in log for log in captured_logs)
    has_yolo = any('YOLOv8' in log for log in captured_logs)
    has_progress = any('100%|' in log and 'it/s' in log for log in captured_logs)
    has_epoch = any('Epoch' in log and 'GPU_mem' in log for log in captured_logs)
    has_results = any('all' in log and '0.0662' in log for log in captured_logs)  # 检查具体的数值
    
    print(f"\n功能验证:")
    print(f"✓ 基本消息捕获: {'成功' if has_basic else '失败'}")
    print(f"✓ YOLO信息捕获: {'成功' if has_yolo else '失败'}")
    print(f"✓ 进度条捕获: {'成功' if has_progress else '失败'}")
    print(f"✓ 训练信息捕获: {'成功' if has_epoch else '失败'}")
    print(f"✓ 结果信息捕获: {'成功' if has_results else '失败'}")
    print(f"✓ 无递归错误: {'成功' if len(captured_logs) < 20 else '失败'}")
    
    print("=" * 60)
    print("最终终端捕获功能测试完成")
    
    if has_basic and has_yolo and has_progress and has_epoch and has_results and len(captured_logs) < 20:
        print("🎉 所有测试通过！")
        print("终端捕获功能工作正常，可以捕获完整的YOLO训练输出。")
        print("现在GUI中应该能看到完整的训练过程，包括：")
        print("- 模型信息")
        print("- 训练进度")
        print("- 验证结果")
        print("- 所有YOLO输出")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
    
    print("=" * 60)
    
    return has_basic and has_yolo and has_progress and has_epoch and has_results and len(captured_logs) < 20

if __name__ == "__main__":
    success = test_final_capture()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    
    if success:
        print("\n✅ 终端捕获功能修复成功！")
        print("现在可以重新启动训练，应该会在GUI中看到完整的YOLO输出。")
        print("包括所有训练过程、进度条、验证结果等。")
    else:
        print("\n❌ 还需要进一步调试和修复。")
