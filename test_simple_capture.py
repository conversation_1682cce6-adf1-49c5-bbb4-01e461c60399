#!/usr/bin/env python3
"""
简单的终端捕获测试
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_simple_capture():
    """测试简化的终端捕获功能"""
    print("=" * 60)
    print("测试简化的终端捕获功能")
    print("=" * 60)
    
    # 捕获的日志
    captured_logs = []
    
    def safe_log_capture(message):
        """安全的日志捕获，避免递归"""
        captured_logs.append(message)
        # 直接写入到stderr，避免被捕获
        sys.stderr.write(f"[CAPTURED] {message}\n")
        sys.stderr.flush()
    
    # 创建简化的终端捕获器
    from ui.widgets.training_widget import TerminalCapture
    terminal_capture = TerminalCapture(safe_log_capture)
    
    print("开始测试简化的终端捕获功能...")
    
    try:
        # 开始捕获
        terminal_capture.start_capture()
        
        # 测试基本输出
        print("这是第一条测试消息")
        print("这是第二条测试消息")
        
        # 测试YOLO风格的输出
        print("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
        print("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
        print("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 测试进度条输出
        print("      1/100      0.85G      1.919      3.481      4.364      1.791         27        640: 100%|##########| 96/96 [00:40<00:00,  2.35it/s]")
        print("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:01<00:00,  2.99it/s]")
        print("                   all         24        196     0.0496      0.503      0.106      0.076     0.0462      0.427     0.0961     0.0534")
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止捕获
        terminal_capture.stop_capture()
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("简化终端捕获测试结果:")
    print(f"捕获的日志条数: {len(captured_logs)}")
    
    # 显示捕获的日志
    print("\n捕获的日志内容:")
    for i, log in enumerate(captured_logs):
        print(f"  {i+1:2d}. {log}")
    
    # 检查关键功能
    has_basic = any('测试消息' in log for log in captured_logs)
    has_yolo = any('YOLOv8' in log for log in captured_logs)
    has_progress = any('%|' in log and 'it/s' in log for log in captured_logs)
    has_epoch = any('Epoch' in log and 'GPU_mem' in log for log in captured_logs)
    
    print(f"\n功能验证:")
    print(f"✓ 基本消息捕获: {'成功' if has_basic else '失败'}")
    print(f"✓ YOLO信息捕获: {'成功' if has_yolo else '失败'}")
    print(f"✓ 进度条捕获: {'成功' if has_progress else '失败'}")
    print(f"✓ 训练信息捕获: {'成功' if has_epoch else '失败'}")
    print(f"✓ 无递归错误: {'成功' if len(captured_logs) < 50 else '失败'}")
    
    print("=" * 60)
    print("简化终端捕获功能测试完成")
    
    if has_basic and has_yolo and has_progress and has_epoch and len(captured_logs) < 50:
        print("🎉 所有测试通过！")
        print("简化的终端捕获功能工作正常。")
        print("现在可以正常捕获YOLO训练输出，避免了递归问题。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
    
    print("=" * 60)
    
    return has_basic and has_yolo and has_progress and len(captured_logs) < 50

if __name__ == "__main__":
    success = test_simple_capture()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    
    if success:
        print("\n✅ 简化的终端捕获功能工作正常！")
        print("现在可以重新启动GUI应用程序进行训练测试。")
        print("虽然没有实现真正的动态更新，但至少可以正常捕获所有YOLO输出。")
    else:
        print("\n❌ 还需要进一步调试和修复。")
