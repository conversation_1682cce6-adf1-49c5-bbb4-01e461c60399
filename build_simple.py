#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO目标检测系统 - 简化打包脚本
快速打包成Windows可执行文件
"""

import os
import subprocess
import sys

# 设置环境变量解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def main():
    """简化的打包流程"""
    print("YOLO目标检测系统 - 快速打包")
    print("=" * 50)
    
    # 检查是否在正确目录
    if not os.path.exists("main.py"):
        print("错误: 请在项目根目录运行此脚本")
        return False
    
    # 直接使用PyInstaller命令打包
    command = [
        "pyinstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # 不显示控制台
        "--name=YOLO目标检测系统",
        "--add-data=config.py;.",  # 包含配置文件
        "--hidden-import=PySide6.QtCore",
        "--hidden-import=PySide6.QtGui", 
        "--hidden-import=PySide6.QtWidgets",
        "--hidden-import=ultralytics",
        "--hidden-import=cv2",
        "--hidden-import=torch",
        "--hidden-import=numpy",
        "--hidden-import=PIL",
        "--hidden-import=onnx",
        "--hidden-import=onnxruntime",
        "--hidden-import=openvino",
        "--hidden-import=ncnn",
        "--clean",
        "--noconfirm",
        "main.py"
    ]
    
    print("开始打包...")
    print("命令:", " ".join(command))
    
    try:
        result = subprocess.run(command, check=True)
        print("\n打包成功!")
        print("可执行文件位置: dist/YOLO目标检测系统.exe")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n打包失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input(f"\n按回车键退出... (打包{'成功' if success else '失败'})")
