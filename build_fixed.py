#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO目标检测系统 - 修复版打包脚本
解决unittest模块缺失和编码问题
"""

import os
import subprocess
import sys
import shutil
from pathlib import Path

# 设置环境变量解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def print_step(step, description):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step}: {description}")
    print(f"{'='*60}")

def clean_build():
    """清理构建目录"""
    print_step(1, "清理构建目录")

    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"删除目录: {dir_name}")
            try:
                shutil.rmtree(dir_name)
            except PermissionError:
                print(f"警告: 无法删除 {dir_name}，可能有文件被占用")

    # 尝试删除dist目录中的文件，但不强制删除
    if os.path.exists('dist'):
        print("尝试清理dist目录...")
        try:
            for file in os.listdir('dist'):
                file_path = os.path.join('dist', file)
                if os.path.isfile(file_path):
                    try:
                        os.remove(file_path)
                        print(f"删除文件: {file}")
                    except PermissionError:
                        print(f"警告: 无法删除 {file}，文件可能正在使用中")
        except Exception as e:
            print(f"清理dist目录时出错: {e}")

    # 删除spec文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        if spec_file != 'build_config.spec':  # 保留我们的配置文件
            print(f"删除spec文件: {spec_file}")
            try:
                os.remove(spec_file)
            except PermissionError:
                print(f"警告: 无法删除 {spec_file}")

    print("清理完成")
    return True

def build_with_pyinstaller():
    """使用PyInstaller构建"""
    print_step(2, "使用PyInstaller构建可执行文件")
    
    # 构建命令
    cmd = [
        'pyinstaller',
        '--onefile',  # 单文件模式
        '--windowed',  # 无控制台窗口
        '--name=YOLO目标检测系统',
        
        # 添加必要的隐藏导入
        '--hidden-import=unittest',
        '--hidden-import=unittest.mock',
        '--hidden-import=collections',
        '--hidden-import=collections.abc',
        '--hidden-import=importlib',
        '--hidden-import=importlib.util',
        '--hidden-import=importlib.metadata',
        '--hidden-import=typing',
        '--hidden-import=typing_extensions',
        
        # PySide6
        '--hidden-import=PySide6.QtCore',
        '--hidden-import=PySide6.QtGui',
        '--hidden-import=PySide6.QtWidgets',
        '--hidden-import=PySide6.QtOpenGL',
        '--hidden-import=PySide6.QtOpenGLWidgets',
        
        # PyTorch相关
        '--hidden-import=torch',
        '--hidden-import=torch.nn',
        '--hidden-import=torch.nn.functional',
        '--hidden-import=torch.fx',
        '--hidden-import=torch.fx.passes',
        '--hidden-import=torch.fx.passes.shape_prop',
        '--hidden-import=torch._dispatch',
        '--hidden-import=torch._dispatch.python',
        '--hidden-import=torch.export',
        '--hidden-import=torchvision',
        '--hidden-import=torchvision.transforms',
        
        # 其他依赖
        '--hidden-import=ultralytics',
        '--hidden-import=cv2',
        '--hidden-import=numpy',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=onnx',
        '--hidden-import=onnxruntime',
        '--hidden-import=openvino',
        '--hidden-import=ncnn',
        
        # 项目模块
        '--hidden-import=core',
        '--hidden-import=core.detector',
        '--hidden-import=core.model_converter',
        '--hidden-import=core.utils',
        '--hidden-import=ui',
        '--hidden-import=ui.main_window',
        '--hidden-import=ui.widgets',
        
        # 其他选项
        '--clean',
        '--noconfirm',
        '--distpath=dist',
        '--workpath=build',
        
        'main.py'
    ]
    
    print("开始构建...")
    print("这可能需要10-20分钟，请耐心等待...")
    
    try:
        # 使用Popen避免编码问题
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            encoding='utf-8',
            errors='ignore'
        )
        
        # 实时输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        return_code = process.poll()
        
        if return_code == 0:
            print("\n构建成功!")
            return True
        else:
            print(f"\n构建失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"构建过程出错: {e}")
        return False

def check_result():
    """检查构建结果"""
    print_step(3, "检查构建结果")
    
    exe_path = Path("dist/YOLO目标检测系统.exe")
    
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        print(f"✓ 可执行文件已生成: {exe_path}")
        print(f"✓ 文件大小: {file_size:.1f} MB")
        return True
    else:
        print("✗ 可执行文件未找到")
        return False

def create_batch_file():
    """创建启动批处理文件"""
    print_step(4, "创建启动脚本")
    
    batch_content = '''@echo off
chcp 65001 > nul
echo 启动YOLO目标检测系统...
echo.
"YOLO目标检测系统.exe"
if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查系统要求
    pause
)
'''
    
    batch_path = Path("dist/启动程序.bat")
    try:
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        print(f"✓ 启动脚本已创建: {batch_path}")
        return True
    except Exception as e:
        print(f"✗ 创建启动脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("YOLO目标检测系统 - 修复版打包脚本")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查是否在正确的目录
    if not Path("main.py").exists():
        print("错误: 请在项目根目录运行此脚本")
        input("按回车键退出...")
        return False
    
    # 执行构建步骤
    steps = [
        clean_build,
        build_with_pyinstaller,
        check_result,
        create_batch_file
    ]
    
    for step_func in steps:
        if not step_func():
            print(f"\n构建失败于步骤: {step_func.__name__}")
            input("按回车键退出...")
            return False
    
    print_step("完成", "构建成功!")
    print("\n可执行文件位置: dist/YOLO目标检测系统.exe")
    print("启动脚本位置: dist/启动程序.bat")
    print("\n您可以将dist目录中的文件分发给其他用户")
    print("建议使用启动脚本来运行程序，可以看到错误信息")
    
    input("\n按回车键退出...")
    return True

if __name__ == "__main__":
    main()
