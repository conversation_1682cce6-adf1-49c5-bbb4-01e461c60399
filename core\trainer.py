# YOLO模型训练器核心模块
import os
import sys
import yaml
import shutil
import requests
from pathlib import Path
from typing import Optional, Dict, Any, Callable, List
from ultralytics import <PERSON><PERSON><PERSON>
import torch
from .utils import get_device, create_output_dir
from .csv_monitor import CombinedTrainingMonitor


class YOLOTrainer:
    """YOLO模型训练器"""
    
    def __init__(self):
        self.model = None
        self.model_path = None
        self.device = get_device()
        self.is_training = False
        self.training_results = None
        
        # 预训练模型下载URLs
        self.pretrained_models = {
            'YOLOv8n': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n.pt',
            'YOLOv8s': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s.pt',
            'YOLOv8m': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8m.pt',
            'YOLOv8l': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8l.pt',
            'YOLOv8x': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8x.pt',
            'YOLOv8n-seg': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n-seg.pt',
            'YOLOv8s-seg': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s-seg.pt',
            'YOLOv8m-seg': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8m-seg.pt',
            'YOLOv8l-seg': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8l-seg.pt',
            'YOLOv8x-seg': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8x-seg.pt',
            'YOLOv8n-pose': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n-pose.pt',
            'YOLOv8s-pose': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s-pose.pt',
            'YOLOv8m-pose': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8m-pose.pt',
            'YOLOv8l-pose': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8l-pose.pt',
            'YOLOv8x-pose': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8x-pose.pt',
            'YOLOv8n-cls': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n-cls.pt',
            'YOLOv8s-cls': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s-cls.pt',
            'YOLOv8m-cls': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8m-cls.pt',
            'YOLOv8l-cls': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8l-cls.pt',
            'YOLOv8x-cls': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8x-cls.pt',
        }
        
        # 默认训练配置
        self.default_config = {
            'epochs': 100,
            'batch': 16,
            'imgsz': 640,
            'lr0': 0.01,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3.0,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            'pose': 12.0,
            'kobj': 1.0,
            # 'label_smoothing': 0.0,  # 已弃用，移除
            'nbs': 64,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'val': True,
            'plots': True,
            'save': True,
            'save_period': -1,
            'cache': False,
            'device': 'auto',
            'workers': 8,
            'project': 'runs/train',
            'name': 'exp',
            'exist_ok': False,
            'pretrained': True,
            'optimizer': 'auto',
            'verbose': True,
            'seed': 0,
            'deterministic': True,
            'single_cls': False,
            'rect': False,
            'cos_lr': False,
            'close_mosaic': 10,
            'resume': False,
            'amp': True,
            'fraction': 1.0,
            'profile': False,
            'freeze': None,
            'multi_scale': False,
        }

        # 初始化训练配置为默认配置的副本
        self.training_config = self.default_config.copy()
    
    def load_pretrained_model(self, model_name: str, save_dir: str = "models") -> bool:
        """加载预训练模型"""
        try:
            if model_name not in self.pretrained_models:
                raise ValueError(f"不支持的预训练模型: {model_name}")
            
            # 创建模型保存目录
            save_path = Path(save_dir)
            save_path.mkdir(exist_ok=True)
            
            model_file = save_path / f"{model_name}.pt"
            
            # 如果模型已存在，直接加载
            if model_file.exists():
                self.model = YOLO(str(model_file))
                self.model_path = str(model_file)
                return True
            
            # 下载模型
            url = self.pretrained_models[model_name]
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(model_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 加载模型
            self.model = YOLO(str(model_file))
            self.model_path = str(model_file)
            
            return True
            
        except Exception as e:
            print(f"预训练模型加载失败: {str(e)}")
            return False
    
    def download_pretrained_model(self, model_name: str, save_dir: str = "models",
                                progress_callback: Optional[Callable] = None) -> bool:
        """下载预训练模型（带进度回调）"""
        try:
            if model_name not in self.pretrained_models:
                raise ValueError(f"不支持的预训练模型: {model_name}")

            # 创建模型保存目录
            save_path = Path(save_dir)
            save_path.mkdir(exist_ok=True)

            model_file = save_path / f"{model_name}.pt"

            # 如果模型已存在，直接返回成功
            if model_file.exists():
                if progress_callback:
                    progress_callback(100, "模型已存在")
                return True

            # 尝试使用ultralytics自动下载
            if progress_callback:
                progress_callback(10, "尝试自动下载...")

            try:
                # 使用ultralytics的自动下载功能
                from ultralytics import YOLO
                temp_model = YOLO(f"{model_name}.pt")  # 这会自动下载模型

                # 检查是否下载成功
                if hasattr(temp_model, 'model') and temp_model.model is not None:
                    # 将模型保存到指定位置
                    import shutil
                    default_path = Path.home() / '.ultralytics' / 'models' / f"{model_name}.pt"
                    if default_path.exists():
                        shutil.copy2(default_path, model_file)
                        if progress_callback:
                            progress_callback(100, "模型下载完成")
                        return True

            except Exception as auto_download_error:
                if progress_callback:
                    progress_callback(20, f"自动下载失败，尝试手动下载: {str(auto_download_error)}")

            # 手动下载模型
            url = self.pretrained_models[model_name]

            if progress_callback:
                progress_callback(30, "开始手动下载...")

            # 配置requests以处理代理问题
            session = requests.Session()
            session.trust_env = False  # 忽略环境变量中的代理设置

            # 设置超时和重试
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry

            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)

            response = session.get(url, stream=True, timeout=30)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0

            with open(model_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        if progress_callback and total_size > 0:
                            progress = 30 + int(((downloaded_size / total_size) * 70))
                            progress_callback(progress, f"下载中... {downloaded_size}/{total_size} bytes")

            if progress_callback:
                progress_callback(100, "下载完成")

            return True

        except requests.exceptions.ProxyError as e:
            error_msg = "网络代理错误，请检查网络设置或尝试使用本地模型"
            print(f"代理错误: {str(e)}")
            if progress_callback:
                progress_callback(-1, error_msg)
            return False
        except requests.exceptions.SSLError as e:
            error_msg = "SSL连接错误，请检查网络连接或尝试使用本地模型"
            print(f"SSL错误: {str(e)}")
            if progress_callback:
                progress_callback(-1, error_msg)
            return False
        except requests.exceptions.ConnectionError as e:
            error_msg = "网络连接失败，请检查网络连接或尝试使用本地模型"
            print(f"连接错误: {str(e)}")
            if progress_callback:
                progress_callback(-1, error_msg)
            return False
        except Exception as e:
            error_msg = f"下载失败: {str(e)}"
            print(f"预训练模型下载失败: {str(e)}")
            if progress_callback:
                progress_callback(-1, error_msg)
            return False

    def load_custom_model(self, model_path: str) -> bool:
        """加载自定义模型"""
        try:
            if not os.path.exists(model_path):
                raise ValueError(f"模型文件不存在: {model_path}")

            self.model = YOLO(model_path)
            self.model_path = model_path
            return True

        except Exception as e:
            print(f"自定义模型加载失败: {str(e)}")
            return False

    def validate_dataset(self, dataset_path: str) -> Dict[str, Any]:
        """验证数据集格式"""
        try:
            dataset_path = Path(dataset_path)

            # 检查数据集目录结构
            required_dirs = ['train', 'val']
            optional_dirs = ['test']

            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'info': {}
            }

            # 检查必需目录
            for dir_name in required_dirs:
                dir_path = dataset_path / dir_name
                if not dir_path.exists():
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"缺少必需目录: {dir_name}")
                else:
                    # 检查images和labels子目录
                    images_dir = dir_path / 'images'
                    labels_dir = dir_path / 'labels'

                    if not images_dir.exists():
                        validation_result['valid'] = False
                        validation_result['errors'].append(f"缺少图片目录: {dir_name}/images")

                    if not labels_dir.exists():
                        validation_result['valid'] = False
                        validation_result['errors'].append(f"缺少标签目录: {dir_name}/labels")

                    # 统计文件数量
                    if images_dir.exists():
                        image_files = list(images_dir.glob('*'))
                        image_count = len([f for f in image_files if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']])
                        validation_result['info'][f'{dir_name}_images'] = image_count

                    if labels_dir.exists():
                        label_files = list(labels_dir.glob('*.txt'))
                        validation_result['info'][f'{dir_name}_labels'] = len(label_files)

            # 检查data.yaml文件
            data_yaml = dataset_path / 'data.yaml'
            if not data_yaml.exists():
                validation_result['warnings'].append("建议创建data.yaml配置文件")
            else:
                try:
                    with open(data_yaml, 'r', encoding='utf-8') as f:
                        data_config = yaml.safe_load(f)

                    # 检查必需字段
                    required_fields = ['train', 'val', 'nc', 'names']
                    for field in required_fields:
                        if field not in data_config:
                            validation_result['warnings'].append(f"data.yaml缺少字段: {field}")

                    validation_result['info']['data_config'] = data_config

                except Exception as e:
                    validation_result['warnings'].append(f"data.yaml解析失败: {str(e)}")

            return validation_result

        except Exception as e:
            return {
                'valid': False,
                'errors': [f"数据集验证失败: {str(e)}"],
                'warnings': [],
                'info': {}
            }

    def create_dataset_config(self, dataset_path: str, class_names: List[str],
                            train_path: str = None, val_path: str = None,
                            test_path: str = None) -> str:
        """创建数据集配置文件"""
        try:
            dataset_path = Path(dataset_path)

            # 默认路径
            if train_path is None:
                train_path = str(dataset_path / 'train' / 'images')
            if val_path is None:
                val_path = str(dataset_path / 'val' / 'images')
            if test_path is None:
                test_path = str(dataset_path / 'test' / 'images') if (dataset_path / 'test').exists() else None

            # 创建配置
            config = {
                'path': str(dataset_path),
                'train': train_path,
                'val': val_path,
                'nc': len(class_names),
                'names': class_names
            }

            if test_path and os.path.exists(test_path):
                config['test'] = test_path

            # 保存配置文件
            config_file = dataset_path / 'data.yaml'
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

            return str(config_file)

        except Exception as e:
            print(f"创建数据集配置失败: {str(e)}")
            return None

    def set_training_config(self, config: Dict[str, Any]):
        """设置训练配置"""
        self.training_config = {**self.default_config, **config}

    def get_training_config(self) -> Dict[str, Any]:
        """获取当前训练配置"""
        return self.training_config.copy()

    def validate_training_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证并清理训练配置，移除不支持的参数"""
        # 已弃用或不支持的参数
        deprecated_params = {
            'label_smoothing',  # 已弃用
            'callbacks',        # 不支持直接传递
        }

        # 使用宽松策略：只移除已知的问题参数，保留其他参数
        clean_config = config.copy()
        removed_params = []

        for param in deprecated_params:
            if param in clean_config:
                clean_config.pop(param)
                removed_params.append(f"{param} (已弃用)")

        # 记录移除的参数
        if removed_params:
            print(f"已移除不支持的参数: {', '.join(removed_params)}")

        return clean_config

    def get_pretrained_models(self) -> Dict[str, str]:
        """获取可用的预训练模型列表"""
        return self.pretrained_models.copy()

    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        if self.model is None:
            return {}

        try:
            info = {
                'model_path': self.model_path,
                'device': str(self.device),
                'model_type': 'PyTorch',
            }

            # 尝试获取模型任务类型
            if hasattr(self.model, 'task'):
                info['task'] = self.model.task

            # 尝试获取类别信息
            if hasattr(self.model, 'names'):
                info['classes'] = self.model.names
                info['num_classes'] = len(self.model.names)

            return info

        except Exception as e:
            print(f"获取模型信息失败: {str(e)}")
            return {'error': str(e)}

    def train(self, data_config: str, progress_callback: Optional[Callable] = None,
              log_callback: Optional[Callable] = None,
              metrics_callback: Optional[Callable] = None,
              batch_progress_callback: Optional[Callable] = None) -> bool:
        """开始训练模型"""
        try:
            if self.model is None:
                raise ValueError("请先加载模型")

            if not os.path.exists(data_config):
                raise ValueError(f"数据配置文件不存在: {data_config}")

            self.is_training = True

            # 设置训练配置
            train_config = self.training_config.copy()
            train_config['data'] = data_config

            # 验证并清理训练配置
            train_config = self.validate_training_config(train_config)

            if progress_callback:
                progress_callback(0, "开始训练...")

            if log_callback:
                log_callback("开始训练模型...")
                log_callback(f"训练配置: {train_config}")

            # 创建组合监控器
            monitor = CombinedTrainingMonitor()

            # 连接回调函数
            if log_callback:
                monitor.log_output.connect(log_callback)

            if progress_callback:
                # 连接轮次进度更新
                def on_progress_update(current_epoch, total_epochs, message):
                    progress = int((current_epoch / total_epochs) * 100)
                    progress_callback(progress, message)
                monitor.progress_update.connect(on_progress_update)

                # 将CSV数据转换为进度信息（作为备用）
                def on_epoch_completed(epoch, data):
                    total_epochs = train_config.get('epochs', 100)
                    progress = int((epoch / total_epochs) * 100)
                    message = f"轮次 {epoch}/{total_epochs} 完成"
                    progress_callback(progress, message)
                monitor.epoch_completed.connect(on_epoch_completed)

            if batch_progress_callback:
                monitor.batch_progress_update.connect(batch_progress_callback)

            if metrics_callback:
                monitor.data_updated.connect(metrics_callback)

            # 开始监控
            monitor.start_monitoring()

            try:
                # 开始训练
                results = self.model.train(**train_config)

            finally:
                # 停止监控
                monitor.stop_monitoring()

            self.training_results = results
            self.is_training = False

            if progress_callback:
                progress_callback(100, "训练完成")

            if log_callback:
                log_callback("训练完成!")
                log_callback(f"训练结果保存在: {results.save_dir}")

                # 输出最终指标
                if hasattr(results, 'results_dict'):
                    log_callback("最终训练指标:")
                    for key, value in results.results_dict.items():
                        log_callback(f"  {key}: {value}")

            return True

        except Exception as e:
            self.is_training = False
            error_msg = f"训练失败: {str(e)}"
            print(error_msg)

            if progress_callback:
                progress_callback(-1, error_msg)

            if log_callback:
                log_callback(error_msg)

            return False

    def stop_training(self):
        """停止训练"""
        self.is_training = False
        # 注意：ultralytics的训练过程不容易中断，这里只是设置标志
        print("训练停止请求已发送")

    def get_training_status(self) -> Dict[str, Any]:
        """获取训练状态"""
        return {
            'is_training': self.is_training,
            'has_results': self.training_results is not None,
            'model_loaded': self.model is not None
        }

    def get_training_results(self) -> Optional[Any]:
        """获取训练结果"""
        return self.training_results

    def validate_model(self, data_config: str = None) -> Optional[Any]:
        """验证模型"""
        try:
            if self.model is None:
                raise ValueError("请先加载模型")

            if data_config and not os.path.exists(data_config):
                raise ValueError(f"数据配置文件不存在: {data_config}")

            # 验证模型
            if data_config:
                results = self.model.val(data=data_config)
            else:
                results = self.model.val()

            return results

        except Exception as e:
            print(f"模型验证失败: {str(e)}")
            return None
