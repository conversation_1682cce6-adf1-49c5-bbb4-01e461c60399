# 训练输出捕获器
import sys
import io
import re
from typing import Callable, Optional
from PySide6.QtCore import QObject, Signal


class OutputCapture(QObject):
    """捕获和解析训练输出的类"""
    
    # 信号定义
    log_output = Signal(str)  # 日志输出信号
    progress_update = Signal(int, int, str)  # 进度更新信号 (current_epoch, total_epochs, message)
    metrics_update = Signal(dict)  # 指标更新信号
    
    def __init__(self):
        super().__init__()
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        self.capturing = False
        self.buffer = io.StringIO()
        self.current_epoch = 0
        self.total_epochs = 0
        
        # 编译正则表达式模式
        self.epoch_pattern = re.compile(r'Epoch\s+(\d+)/(\d+)')
        self.progress_pattern = re.compile(r'(\d+)%\|[█▉▊▋▌▍▎▏ ]*\|\s*(\d+)/(\d+)')
        self.metrics_pattern = re.compile(r'(\w+):\s*([\d.]+)')
        self.loss_pattern = re.compile(r'train/(\w+):\s*([\d.]+)')
        self.val_pattern = re.compile(r'val/(\w+):\s*([\d.]+)')
        self.map_pattern = re.compile(r'metrics/(\w+):\s*([\d.]+)')
    
    def start_capture(self):
        """开始捕获输出"""
        if not self.capturing:
            self.capturing = True
            sys.stdout = self
            sys.stderr = self
    
    def stop_capture(self):
        """停止捕获输出"""
        if self.capturing:
            self.capturing = False
            sys.stdout = self.original_stdout
            sys.stderr = self.original_stderr
    
    def write(self, text):
        """重写write方法来捕获输出"""
        # 同时写入原始输出和缓冲区
        self.original_stdout.write(text)
        self.original_stdout.flush()

        # 处理捕获的文本（避免递归）
        if text.strip() and self.capturing:
            try:
                # 暂时停止捕获以避免递归
                self.capturing = False
                self.process_output(text)
            finally:
                # 恢复捕获
                self.capturing = True

        return len(text)
    
    def flush(self):
        """刷新输出"""
        self.original_stdout.flush()
    
    def process_output(self, text):
        """处理捕获的输出文本"""
        try:
            # 发送原始日志（不使用print避免递归）
            self.log_output.emit(text.rstrip())

            # 解析轮次信息
            epoch_match = self.epoch_pattern.search(text)
            if epoch_match:
                self.current_epoch = int(epoch_match.group(1))
                self.total_epochs = int(epoch_match.group(2))
                message = f"训练轮次 {self.current_epoch}/{self.total_epochs}"
                self.progress_update.emit(self.current_epoch, self.total_epochs, message)

            # 解析进度条信息
            progress_match = self.progress_pattern.search(text)
            if progress_match:
                percent = int(progress_match.group(1))
                current = int(progress_match.group(2))
                total = int(progress_match.group(3))

                if self.current_epoch > 0 and self.total_epochs > 0:
                    # 计算总体进度
                    epoch_progress = (self.current_epoch - 1) / self.total_epochs
                    batch_progress = (current / total) / self.total_epochs
                    total_progress = int((epoch_progress + batch_progress) * 100)

                    message = f"轮次 {self.current_epoch}/{self.total_epochs} - 批次 {current}/{total} ({percent}%)"
                    self.progress_update.emit(total_progress, 100, message)

            # 解析训练指标
            self.parse_metrics(text)

        except Exception as e:
            # 如果解析出错，直接写入原始输出（避免递归）
            self.original_stdout.write(f"输出解析错误: {e}\n")
            self.original_stdout.flush()
    
    def parse_metrics(self, text):
        """解析训练指标"""
        try:
            metrics = {}
            
            # 解析损失值
            loss_matches = self.loss_pattern.findall(text)
            for metric_name, value in loss_matches:
                metrics[f'train_{metric_name}'] = float(value)
            
            # 解析验证指标
            val_matches = self.val_pattern.findall(text)
            for metric_name, value in val_matches:
                metrics[f'val_{metric_name}'] = float(value)
            
            # 解析mAP等指标
            map_matches = self.map_pattern.findall(text)
            for metric_name, value in map_matches:
                if 'precision' in metric_name.lower():
                    metrics['precision'] = float(value)
                elif 'recall' in metric_name.lower():
                    metrics['recall'] = float(value)
                elif 'map50' in metric_name.lower() and 'map50-95' not in metric_name.lower():
                    metrics['mAP50'] = float(value)
                elif 'map50-95' in metric_name.lower():
                    metrics['mAP50_95'] = float(value)
            
            # 如果有指标更新，发送信号
            if metrics:
                if self.current_epoch > 0:
                    metrics['epoch'] = self.current_epoch
                self.metrics_update.emit(metrics)
                
        except Exception as e:
            # 避免递归，直接写入原始输出
            self.original_stdout.write(f"指标解析错误: {e}\n")
            self.original_stdout.flush()


class TrainingOutputCapture(QObject):
    """专门用于训练的输出捕获器"""
    
    log_signal = Signal(str)
    progress_signal = Signal(int, str)
    metrics_signal = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.capture = OutputCapture()
        self.setup_connections()
    
    def setup_connections(self):
        """设置信号连接"""
        self.capture.log_output.connect(self.log_signal.emit)
        self.capture.progress_update.connect(self.on_progress_update)
        self.capture.metrics_update.connect(self.metrics_signal.emit)
    
    def on_progress_update(self, current, total, message):
        """处理进度更新"""
        if total > 0:
            progress = int((current / total) * 100)
            self.progress_signal.emit(progress, message)
        else:
            # 即使没有总数，也发送消息更新
            self.progress_signal.emit(0, message)
    
    def start(self):
        """开始捕获"""
        self.capture.start_capture()
    
    def stop(self):
        """停止捕获"""
        self.capture.stop_capture()


class StreamCapture:
    """简单的流捕获器"""
    
    def __init__(self, callback: Optional[Callable] = None):
        self.callback = callback
        self.buffer = []
    
    def write(self, text):
        """写入文本"""
        if self.callback:
            self.callback(text)
        self.buffer.append(text)
        return len(text)
    
    def flush(self):
        """刷新"""
        pass
    
    def getvalue(self):
        """获取缓冲区内容"""
        return ''.join(self.buffer)
    
    def clear(self):
        """清空缓冲区"""
        self.buffer.clear()
