#!/usr/bin/env python3
"""
测试终端输出同步功能
"""

import os
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from core.trainer import YOLOTrainer

def test_terminal_sync():
    """测试终端输出同步功能"""
    print("=" * 60)
    print("测试终端输出同步功能")
    print("=" * 60)
    
    # 创建训练器
    trainer = YOLOTrainer()
    
    # 加载模型
    model_path = "models/yolov8n-seg.pt"
    if not os.path.exists(model_path):
        print(f"错误：模型文件不存在: {model_path}")
        return False
    
    success = trainer.load_custom_model(model_path)
    if not success:
        print("模型加载失败")
        return False
    
    print(f"✓ 模型加载成功: {model_path}")
    
    # 创建简单的数据配置文件
    import yaml
    data_config_dict = {
        'path': 'datasets/coco8-seg',
        'train': 'images/train',
        'val': 'images/val',
        'names': {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck'}
    }
    
    data_config_path = "terminal_sync_test.yaml"
    with open(data_config_path, 'w', encoding='utf-8') as f:
        yaml.dump(data_config_dict, f, default_flow_style=False, allow_unicode=True)
    
    # 设置训练配置
    training_config = {
        'epochs': 2,      # 只训练2个轮次用于测试
        'batch': 4,       # 小批次大小
        'workers': 0,     # 避免多进程问题
        'imgsz': 640,
        'device': 'cpu',
        'cache': False,
        'plots': True,
        'verbose': True,
        'save': True,
        'save_period': 1
    }
    
    trainer.set_training_config(training_config)
    
    # 捕获的日志
    captured_logs = []
    
    def log_capture(message):
        """捕获日志消息"""
        captured_logs.append(message)
        print(f"[CAPTURED] {message}")
    
    # 创建终端捕获器
    from ui.widgets.training_widget import TerminalCapture
    terminal_capture = TerminalCapture(log_capture)
    
    print("\n开始测试终端输出同步...")
    print("注意：所有YOLO的终端输出都会被实时捕获到日志中")
    
    try:
        # 开始捕获
        terminal_capture.start_capture()
        
        # 测试基本输出
        print("这是一条测试消息")
        print("这是另一条测试消息")
        
        # 模拟进度条输出
        for i in range(5):
            print(f"\r进度: {i*20}%|{'█'*i}{'▌'*(5-i)}| {i}/5", end='', flush=True)
            time.sleep(0.5)
        print()  # 换行
        
        print("开始YOLO训练...")
        
        # 开始训练（这会产生大量终端输出）
        success = trainer.train(
            data_config_path,
            None,  # 不使用进度回调
            None   # 不使用日志回调，完全依赖终端捕获
        )
        
        print(f"训练结果: {'成功' if success else '失败'}")
        
    except Exception as e:
        print(f"训练异常: {e}")
        success = False
        
    finally:
        # 停止捕获
        terminal_capture.stop_capture()
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("终端输出同步测试结果:")
    print(f"捕获的日志条数: {len(captured_logs)}")
    print(f"训练结果: {'成功' if success else '失败'}")
    
    # 显示部分捕获的日志
    print("\n前10条捕获的日志:")
    for i, log in enumerate(captured_logs[:10]):
        print(f"  {i+1:2d}. {log}")
    
    if len(captured_logs) > 10:
        print(f"  ... 还有 {len(captured_logs) - 10} 条日志")
    
    # 检查是否捕获到YOLO特有的输出
    yolo_keywords = ['Ultralytics', 'YOLOv8', 'Epoch', 'GPU_mem', 'box_loss', 'seg_loss']
    found_keywords = []
    
    for keyword in yolo_keywords:
        for log in captured_logs:
            if keyword in log:
                found_keywords.append(keyword)
                break
    
    print(f"\n检测到的YOLO关键词: {found_keywords}")
    
    # 检查进度条输出
    progress_logs = [log for log in captured_logs if '[PROGRESS]' in log or '|' in log or '%' in log]
    print(f"进度相关日志条数: {len(progress_logs)}")
    
    if progress_logs:
        print("示例进度日志:")
        for log in progress_logs[:3]:
            print(f"  {log}")
    
    # 清理测试文件
    if os.path.exists(data_config_path):
        os.remove(data_config_path)
        print(f"\n✓ 清理测试文件: {data_config_path}")
    
    print("=" * 60)
    print("终端输出同步功能测试完成")
    print("功能验证:")
    print(f"✓ 基本输出捕获: {'成功' if len(captured_logs) > 0 else '失败'}")
    print(f"✓ YOLO输出捕获: {'成功' if len(found_keywords) > 0 else '失败'}")
    print(f"✓ 进度输出捕获: {'成功' if len(progress_logs) > 0 else '失败'}")
    print("=" * 60)
    
    return len(captured_logs) > 0 and len(found_keywords) > 0

if __name__ == "__main__":
    test_terminal_sync()
