#!/usr/bin/env python3
"""
简单的停止训练功能测试
"""

import os
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from ultralytics import YOLO

def test_stop_training_simple():
    """简单测试停止训练功能"""
    print("=" * 60)
    print("简单测试停止训练功能")
    print("=" * 60)
    
    # 直接使用YOLO模型
    model_path = "models/yolov8n-seg.pt"
    if not os.path.exists(model_path):
        print(f"错误：模型文件不存在: {model_path}")
        return False
    
    model = YOLO(model_path)
    print(f"✓ 模型加载成功: {model_path}")
    
    # 训练状态标志
    training_active = {'value': False}
    training_thread = None
    
    def start_training():
        """启动训练"""
        training_active['value'] = True
        try:
            print("开始训练...")
            # 使用内置的coco8-seg数据集，epochs设置为较大值来测试停止功能
            results = model.train(
                data='coco8-seg',  # 使用内置数据集
                epochs=20,         # 足够长的训练时间
                batch=4,
                imgsz=640,
                device='cpu',
                workers=0,
                cache=False,
                plots=True,
                verbose=True,
                save=True,
                save_period=1
            )
            print("训练完成")
            training_active['value'] = False
            return results
        except Exception as e:
            print(f"训练异常: {e}")
            training_active['value'] = False
            return None
    
    # 在单独的线程中启动训练
    training_thread = threading.Thread(target=start_training)
    training_thread.daemon = True
    training_thread.start()
    
    # 等待训练开始
    print("等待训练开始...")
    start_time = time.time()
    while not training_active['value'] and time.time() - start_time < 30:
        time.sleep(1)
    
    if training_active['value']:
        print("✓ 训练已开始")
        
        # 让训练运行一段时间
        print("让训练运行15秒...")
        time.sleep(15)
        
        # 测试停止功能
        print("\n🛑 测试停止训练功能...")
        print("注意：由于YOLO训练的特性，可能需要等待当前轮次完成")
        
        # 设置停止标志
        training_active['value'] = False
        
        # 等待训练线程结束
        print("等待训练线程结束...")
        training_thread.join(timeout=30)
        
        if training_thread.is_alive():
            print("⚠️ 训练线程仍在运行（这是正常的，YOLO训练可能需要完成当前轮次）")
        else:
            print("✓ 训练线程已结束")
    else:
        print("❌ 训练未能启动")
    
    # 检查exp文件夹
    runs_dir = Path("runs/train")
    if runs_dir.exists():
        exp_dirs = list(runs_dir.glob("exp*"))
        if exp_dirs:
            latest_exp = max(exp_dirs, key=lambda x: x.stat().st_mtime)
            csv_file = latest_exp / "results.csv"
            print(f"\n📁 最新训练文件夹: {latest_exp}")
            if csv_file.exists():
                print(f"✓ CSV文件已生成: {csv_file}")
                # 读取CSV文件内容
                try:
                    import pandas as pd
                    df = pd.read_csv(csv_file)
                    print(f"  CSV文件包含 {len(df)} 行数据（训练轮次）")
                    if len(df) > 0:
                        print(f"  最后一轮的指标: {dict(df.iloc[-1])}")
                except Exception as e:
                    print(f"  读取CSV文件失败: {e}")
            else:
                print(f"❌ CSV文件未生成: {csv_file}")
        else:
            print("❌ 未找到exp文件夹")
    else:
        print("❌ runs/train目录不存在")
    
    print("\n" + "=" * 60)
    print("测试说明:")
    print("1. 这个测试展示了YOLO训练的基本行为")
    print("2. YOLO训练通常会完成当前轮次后才能停止")
    print("3. 在实际GUI中，我们需要实现更强力的停止机制")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_stop_training_simple()
