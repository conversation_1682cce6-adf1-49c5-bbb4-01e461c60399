absl-py==2.3.0
altgraph==0.17.4
anyio==4.5.2
astor==0.8.1
astunparse==1.6.3
attrs==25.3.0
Brotli @ file:///C:/b/abs_3d36mno480/croot/brotli-split_1714483178642/work
cachetools==5.5.2
cattrs==24.1.3
certifi==2025.4.26
charset-normalizer==3.4.2
colorama==0.4.6
coloredlogs==15.0.1
contourpy==1.1.1
cycler==0.12.1
darkdetect==0.8.0
decorator==5.2.1
exceptiongroup==1.3.0
filelock @ file:///C:/b/abs_f2gie28u58/croot/filelock_1700591233643/work
flatbuffers==25.2.10
fonttools==4.57.0
fsspec==2025.3.0
gast==0.4.0
gmpy2 @ file:///C:/ci/gmpy2_1645456279018/work
google-auth==2.40.2
google-auth-oauthlib==1.0.0
google-pasta==0.2.0
grpcio==1.70.0
h11==0.16.0
h5py==3.11.0
httpcore==1.0.9
httpx==0.28.1
humanfriendly==10.0
idna==3.10
importlib_metadata==8.5.0
importlib_resources==6.4.5
Jinja2 @ file:///C:/b/abs_92fccttino/croot/jinja2_1716993447201/work
keras==2.13.1
kiwisolver==1.4.7
lap==0.5.12
libclang==18.1.1
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe @ file:///C:/b/abs_ecfdqh67b_/croot/markupsafe_1704206030535/work
matplotlib==3.7.5
mdurl==0.1.2
mkl-fft @ file:///C:/b/abs_19i1y8ykas/croot/mkl_fft_1695058226480/work
mkl-random @ file:///C:/b/abs_edwkj1_o69/croot/mkl_random_1695059866750/work
mkl-service==2.4.0
mpmath==1.3.0
ncnn==1.0.20250503
networkx @ file:///C:/b/abs_e6gi1go5op/croot/networkx_1690562046966/work
numpy==1.24.3
oauthlib==3.2.2
onnx==1.16.1
onnxruntime==1.16.3
onnxruntime-gpu==1.19.2
onnxsim==0.4.36
onnxslim==0.1.54
opencv-python==4.7.0
openvino==2024.4.0
openvino-telemetry==2025.1.0
opt-einsum==3.3.0
packaging @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_packaging_1745345660/work
pandas==2.0.3
pefile==2023.2.7
Pillow @ file:///D:/bld/pillow_1675487410089/work
portalocker==3.0.0
protobuf==3.20.3
psutil==7.0.0
py-cpuinfo==9.0.0
pyaml==25.5.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
Pygments==2.19.1
pyinstaller==6.13.0
pyinstaller-hooks-contrib==2025.4
pyparsing==3.1.4
PyQt-Fluent-Widgets==1.8.1
PyQt5==5.15.11
PyQt5-Frameless-Window==0.7.3
PyQt5-Qt5==5.15.2
PyQt5_sip==12.15.0
pyreadline3==3.5.4
PySide6==*******
PySide6_Addons==*******
PySide6_Essentials==*******
PySocks @ file:///C:/ci/pysocks_1605287845585/work
python-dateutil==2.9.0.post0
pytz==2025.2
pywin32==310
pywin32-ctypes==0.2.3
PyYAML @ file:///C:/b/abs_14xkfs39bx/croot/pyyaml_1728657968772/work
requests==2.32.3
requests-oauthlib==2.0.0
rich==14.0.0
rsa==4.9.1
scipy==1.10.1
seaborn==0.13.2
shiboken6==*******
six==1.17.0
sniffio==1.3.1
sympy==1.13.3
tensorboard==2.13.0
tensorboard-data-server==0.7.2
tensorflow-estimator==2.13.0
tensorflow-intel==2.13.0
tensorflow-io-gcs-filesystem==0.31.0
termcolor==2.4.0
torch==2.4.1
tqdm==4.67.1
typing_extensions @ file:///home/<USER>/feedstock_root/build_artifacts/typing_extensions_1717802530399/work
tzdata==2025.2
ultralytics==8.3.133
ultralytics-thop==2.0.14
urllib3==2.2.3
Werkzeug==3.0.6
win-inet-pton @ file:///C:/ci/win_inet_pton_1605306167264/work
wrapt==1.17.2
zipp==3.20.2
