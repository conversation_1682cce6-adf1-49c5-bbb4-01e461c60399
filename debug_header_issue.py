#!/usr/bin/env python3
"""
调试表头显示问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def debug_header_issue():
    """调试表头显示问题"""
    print("=" * 60)
    print("调试表头显示问题")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练组件
        widget = TrainingWidget()
        
        # 测试实际的YOLO输出序列
        test_messages = [
            "Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU",
            "Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs",
            "      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size",
            "      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:   0%|          | 0/96 [00:00<?, ?it/s]",
            "      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:  10%|#         | 10/96 [00:04<00:37,  2.32it/s]",
            "      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:  58%|#####8    | 56/96 [00:24<00:17,  2.32it/s]",
        ]
        
        print("测试消息的进度行检测结果:")
        for i, message in enumerate(test_messages):
            is_progress = widget._is_progress_line(message)
            print(f"{i+1}. {'[PROGRESS]' if is_progress else '[NORMAL]  '} {message[:80]}...")
        
        print("\n开始模拟实际添加过程:")
        
        for i, message in enumerate(test_messages):
            print(f"\n步骤 {i+1}: 添加消息")
            print(f"消息: {message[:80]}...")
            
            widget.add_log(message)
            
            # 检查当前日志内容
            current_text = widget.log_text.toPlainText()
            lines = current_text.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            print(f"当前日志行数: {len(non_empty_lines)}")
            print("当前日志内容:")
            for j, line in enumerate(non_empty_lines):
                print(f"  {j+1}. {line}")
        
        # 最终检查
        print("\n" + "=" * 60)
        print("最终结果分析")
        print("=" * 60)
        
        final_text = widget.log_text.toPlainText()
        final_lines = final_text.split('\n')
        final_non_empty = [line for line in final_lines if line.strip()]
        
        print(f"最终日志行数: {len(final_non_empty)}")
        print("最终日志内容:")
        for i, line in enumerate(final_non_empty):
            print(f"  {i+1}. {line}")
        
        # 检查是否包含表头
        has_header = any('Epoch    GPU_mem   box_loss' in line for line in final_non_empty)
        has_progress = any('58%|#####8' in line for line in final_non_empty)
        
        print(f"\n关键检查:")
        print(f"包含表头: {'是' if has_header else '否'}")
        print(f"包含进度: {'是' if has_progress else '否'}")
        
        if not has_header:
            print("\n❌ 问题确认：表头确实丢失了！")
            print("可能的原因：")
            print("1. 表头被错误识别为进度行")
            print("2. 表头被后续的进度行替换")
            print("3. 进度行检测逻辑有问题")
        else:
            print("\n✅ 表头正常显示")
        
        return has_header
        
    except Exception as e:
        print(f"调试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detection_logic():
    """测试检测逻辑"""
    print("\n" + "=" * 60)
    print("测试检测逻辑")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练组件
        widget = TrainingWidget()
        
        # 测试关键消息
        test_cases = [
            ("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size", "表头"),
            ("      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:   0%|          | 0/96 [00:00<?, ?it/s]", "进度行"),
            ("      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:  58%|#####8    | 56/96 [00:24<00:17,  2.32it/s]", "进度行"),
        ]
        
        print("详细检测分析:")
        for message, expected_type in test_cases:
            print(f"\n消息类型: {expected_type}")
            print(f"消息内容: {message}")
            
            # 检查各种条件
            has_epoch_gpu = 'Epoch' in message and 'GPU_mem' in message and 'box_loss' in message
            has_progress_bar = '%|' in message
            has_iteration_speed = 'it/s' in message
            has_epoch_format = bool(__import__('re').search(r'\d+/\d+', message))
            
            print(f"包含Epoch+GPU_mem+box_loss: {has_epoch_gpu}")
            print(f"包含进度条(%|): {has_progress_bar}")
            print(f"包含迭代速度(it/s): {has_iteration_speed}")
            print(f"包含epoch格式(数字/数字): {has_epoch_format}")
            
            is_progress = widget._is_progress_line(message)
            print(f"最终判断为进度行: {is_progress}")
            
            if expected_type == "表头" and is_progress:
                print("❌ 错误：表头被误识别为进度行！")
            elif expected_type == "进度行" and not is_progress:
                print("❌ 错误：进度行未被识别！")
            else:
                print("✅ 识别正确")
        
    except Exception as e:
        print(f"测试异常: {e}")

if __name__ == "__main__":
    print("开始调试表头显示问题...")
    
    # 测试检测逻辑
    test_detection_logic()
    
    # 调试实际问题
    has_header = debug_header_issue()
    
    print("\n" + "=" * 60)
    print("调试总结")
    print("=" * 60)
    
    if not has_header:
        print("❌ 确认问题：表头确实丢失了")
        print("需要修复进度行检测逻辑")
    else:
        print("✅ 表头显示正常")
    
    print("=" * 60)
