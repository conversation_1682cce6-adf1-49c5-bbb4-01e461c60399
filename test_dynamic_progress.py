#!/usr/bin/env python3
"""
测试动态进度更新功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_dynamic_progress():
    """测试动态进度更新功能"""
    print("=" * 60)
    print("测试动态进度更新功能")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练组件
        widget = TrainingWidget()
        
        print("开始测试动态进度更新...")
        
        # 测试静态日志
        widget.add_log("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
        widget.add_log("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
        widget.add_log("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        print("✓ 添加了静态日志")
        
        # 测试动态进度更新
        base_line = "1/100      0.85G      1.919      3.481      4.364      1.791         27        640: "
        
        for i in range(0, 101, 10):
            progress_bar = f"{i:3d}%|{'#' * (i//10)}{'.' * (10-i//10)}| {i}/96 [00:30<00:10,  2.35it/s]"
            full_line = base_line + progress_bar
            
            widget.add_log(full_line)
            print(f"✓ 更新进度: {i}%")
            time.sleep(0.2)
        
        # 添加验证阶段
        widget.add_log("Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:01<00:00,  2.99it/s]")
        widget.add_log("all         24        196     0.0662     0.0864     0.0428     0.0302     0.0116     0.0451    0.00739    0.00122")
        
        print("✓ 添加了验证结果")
        
        # 检查最终结果
        final_text = widget.log_text.toPlainText()
        lines = final_text.split('\n')
        
        print(f"\n最终日志行数: {len(lines)}")
        print("最后10行内容:")
        for i, line in enumerate(lines[-10:]):
            print(f"  {len(lines)-10+i+1:2d}. {line}")
        
        # 检查是否有重复的进度行
        progress_lines = [line for line in lines if '1/100' in line and '%|' in line and 'it/s' in line]
        print(f"\n进度行数量: {len(progress_lines)}")
        
        if len(progress_lines) <= 2:  # 应该只有1-2行（最多一个初始行和一个最终行）
            print("✅ 动态更新成功！进度行没有重复。")
            success = True
        else:
            print("❌ 动态更新失败，存在重复的进度行。")
            print("重复的进度行:")
            for i, line in enumerate(progress_lines):
                print(f"  {i+1}. {line}")
            success = False
        
        # 检查是否包含完整的100%进度
        final_progress = any('100%|##########|' in line for line in lines)
        print(f"包含最终进度: {'是' if final_progress else '否'}")
        
        return success and final_progress
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_progress_detection():
    """测试进度行检测功能"""
    print("\n" + "=" * 60)
    print("测试进度行检测功能")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练组件
        widget = TrainingWidget()
        
        # 测试用例
        test_cases = [
            ("1/100      0.85G      1.919      3.481      4.364      1.791         27        640:   0%|          | 0/96 [00:00<?, ?it/s]", True),
            ("1/100      0.85G      1.919      3.481      4.364      1.791         27        640:  50%|#####     | 48/96 [00:20<00:20,  2.35it/s]", True),
            ("Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:01<00:00,  2.99it/s]", True),
            ("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU", False),
            ("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs", False),
            ("Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size", False),
            ("all         24        196     0.0662     0.0864     0.0428     0.0302     0.0116     0.0451    0.00739    0.00122", False),
        ]
        
        print("测试进度行检测:")
        all_correct = True
        
        for message, expected in test_cases:
            result = widget._is_progress_line(message)
            status = "✅" if result == expected else "❌"
            print(f"{status} {result} | {message[:80]}...")
            if result != expected:
                all_correct = False
        
        print(f"\n进度行检测准确性: {'✅ 全部正确' if all_correct else '❌ 存在错误'}")
        return all_correct
        
    except Exception as e:
        print(f"测试异常: {e}")
        return False

if __name__ == "__main__":
    print("开始测试动态进度更新功能...")
    
    # 测试进度行检测
    detection_ok = test_progress_detection()
    
    # 测试动态更新
    update_ok = test_dynamic_progress()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"进度行检测: {'✅ 通过' if detection_ok else '❌ 失败'}")
    print(f"动态更新: {'✅ 通过' if update_ok else '❌ 失败'}")
    
    if detection_ok and update_ok:
        print("\n🎉 所有测试通过！")
        print("动态进度更新功能已经实现！")
        print("现在YOLO训练的进度条将在同一行动态刷新。")
        print("您将看到类似这样的效果：")
        print("      1/100      0.85G      1.919      3.481      4.364      1.791         27        640: 100%|##########| 96/96 [00:40<00:00,  2.35it/s]")
        print("进度条会在同一行动态更新，而不是创建新行。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")
    
    print("=" * 60)
