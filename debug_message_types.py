#!/usr/bin/env python3
"""
调试消息类型检测
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def debug_message_types():
    """调试消息类型检测"""
    print("=" * 60)
    print("调试消息类型检测")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练组件
        widget = TrainingWidget()
        
        # 测试消息
        test_messages = [
            "Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU",
            "Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs",
            "      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size",
            "      1/100      1.05G      1.523      3.141      4.765      1.325         22        640:   0%|          | 0/96 [00:39<00:00,  2.40it/s]",
            "      1/100      1.05G      1.523      3.141      4.765      1.325         22        640: 100%|##########| 96/96 [00:39<00:00,  2.40it/s]",
            "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:00<00:00,  3.20it/s]",
            "                   all         24        196     0.0662     0.0864     0.0428     0.0302     0.0116     0.0451    0.00739    0.00122",
        ]
        
        print("消息类型检测结果:")
        for i, message in enumerate(test_messages):
            print(f"\n{i+1}. 消息: {message[:80]}...")
            
            is_training_header = widget._is_training_header(message)
            is_training_progress = widget._is_training_progress(message)
            is_validation_header = widget._is_validation_header(message)
            is_validation_progress = widget._is_validation_progress(message)
            is_validation_result = widget._is_validation_result(message)
            
            print(f"   训练表头: {is_training_header}")
            print(f"   训练进度: {is_training_progress}")
            print(f"   验证表头: {is_validation_header}")
            print(f"   验证进度: {is_validation_progress}")
            print(f"   验证结果: {is_validation_result}")
            
            # 检查是否有多个类型匹配
            matches = sum([is_training_header, is_training_progress, is_validation_header, 
                          is_validation_progress, is_validation_result])
            
            if matches > 1:
                print(f"   ❌ 错误：多个类型匹配 ({matches})")
            elif matches == 0:
                print(f"   ⚪ 其他类型")
            else:
                print(f"   ✅ 类型唯一")
        
        # 特别检查训练进度行的检测
        print("\n" + "=" * 60)
        print("训练进度行检测详细分析")
        print("=" * 60)
        
        training_progress_msg = "      1/100      1.05G      1.523      3.141      4.765      1.325         22        640: 100%|##########| 96/96 [00:39<00:00,  2.40it/s]"
        
        print(f"消息: {training_progress_msg}")
        print(f"包含 '/100': {'/100' in training_progress_msg}")
        print(f"包含 '%|': {'%|' in training_progress_msg}")
        print(f"包含 'it/s': {'it/s' in training_progress_msg}")
        print(f"包含 'Class': {'Class' in training_progress_msg}")
        print(f"包含 'Images': {'Images' in training_progress_msg}")
        
        condition1 = '/100' in training_progress_msg
        condition2 = '%|' in training_progress_msg
        condition3 = 'it/s' in training_progress_msg
        condition4 = 'Class' not in training_progress_msg
        condition5 = 'Images' not in training_progress_msg
        
        print(f"条件1 (/100): {condition1}")
        print(f"条件2 (%|): {condition2}")
        print(f"条件3 (it/s): {condition3}")
        print(f"条件4 (not Class): {condition4}")
        print(f"条件5 (not Images): {condition5}")
        print(f"最终结果: {condition1 and condition2 and condition3 and condition4 and condition5}")
        
        # 检查验证结果行的检测
        print("\n" + "=" * 60)
        print("验证结果行检测详细分析")
        print("=" * 60)
        
        validation_result_msg = "                   all         24        196     0.0662     0.0864     0.0428     0.0302     0.0116     0.0451    0.00739    0.00122"
        
        print(f"消息: {validation_result_msg}")
        print(f"包含 'all': {'all' in validation_result_msg}")
        print(f"分割长度: {len(validation_result_msg.split())}")
        print(f"包含数字: {any(char.isdigit() for char in validation_result_msg)}")
        print(f"包含 '%|': {'%|' in validation_result_msg}")
        
        v_condition1 = 'all' in validation_result_msg
        v_condition2 = len(validation_result_msg.split()) > 10
        v_condition3 = any(char.isdigit() for char in validation_result_msg)
        v_condition4 = '%|' not in validation_result_msg
        
        print(f"条件1 (all): {v_condition1}")
        print(f"条件2 (长度>10): {v_condition2}")
        print(f"条件3 (包含数字): {v_condition3}")
        print(f"条件4 (not %|): {v_condition4}")
        print(f"最终结果: {v_condition1 and v_condition2 and v_condition3 and v_condition4}")
        
        return True
        
    except Exception as e:
        print(f"调试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始调试消息类型检测...")
    
    success = debug_message_types()
    
    print("\n" + "=" * 60)
    print("调试总结")
    print("=" * 60)
    
    if success:
        print("✅ 调试完成")
    else:
        print("❌ 调试失败")
    
    print("=" * 60)
