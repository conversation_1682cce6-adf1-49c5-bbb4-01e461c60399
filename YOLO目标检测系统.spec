# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['unittest', 'unittest.mock', 'collections', 'collections.abc', 'importlib', 'importlib.util', 'importlib.metadata', 'typing', 'typing_extensions', 'PySide6.QtCore', 'PySide6.QtGui', 'PySide6.QtWidgets', 'torch', 'torch.fx', 'torch.fx.passes', 'torch.fx.passes.shape_prop', 'torch._dispatch', 'torch._dispatch.python', 'torch.export', 'torchvision', 'ultralytics', 'cv2', 'numpy', 'PIL', 'onnx', 'onnxruntime', 'openvino', 'ncnn', 'core', 'ui'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='YOLO目标检测系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
