#!/usr/bin/env python3
"""
测试简化后的训练功能
"""

import os
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from core.trainer import YOLOTrainer

def test_simple_training():
    """测试简化的训练功能"""
    print("=" * 60)
    print("测试简化的训练功能")
    print("=" * 60)
    
    # 创建训练器
    trainer = YOLOTrainer()
    
    # 加载模型
    model_path = "models/yolov8n-seg.pt"
    if not os.path.exists(model_path):
        print(f"错误：模型文件不存在: {model_path}")
        return False
    
    success = trainer.load_custom_model(model_path)
    if not success:
        print("模型加载失败")
        return False
    
    print(f"✓ 模型加载成功: {model_path}")
    
    # 创建简单的数据配置文件
    import yaml
    data_config_dict = {
        'path': 'datasets/coco8-seg',
        'train': 'images/train',
        'val': 'images/val',
        'names': {0: 'person', 1: 'bicycle', 2: 'car', 3: 'motorcycle', 4: 'airplane', 5: 'bus', 6: 'train', 7: 'truck'}
    }
    
    data_config_path = "simple_test_data.yaml"
    with open(data_config_path, 'w', encoding='utf-8') as f:
        yaml.dump(data_config_dict, f, default_flow_style=False, allow_unicode=True)
    
    # 设置训练配置
    training_config = {
        'epochs': 2,      # 只训练2个轮次用于测试
        'batch': 4,       # 小批次大小
        'workers': 0,     # 避免多进程问题
        'imgsz': 640,
        'device': 'cpu',
        'cache': False,
        'plots': True,
        'verbose': True,
        'save': True,
        'save_period': 1
    }
    
    trainer.set_training_config(training_config)
    
    # 回调函数
    log_messages = []
    progress_updates = []
    
    def log_callback(message):
        log_messages.append(message)
        print(f"[LOG] {message}")
    
    def progress_callback(progress, message):
        progress_updates.append((progress, message))
        print(f"[PROGRESS] {progress}% - {message}")
    
    print("\n开始训练...")
    print("注意：这是简化版本，没有实时监控功能")
    
    try:
        # 开始训练
        success = trainer.train(
            data_config_path,
            progress_callback,
            log_callback
        )
        
        print(f"\n训练结果: {'成功' if success else '失败'}")
        
    except Exception as e:
        print(f"训练异常: {e}")
        success = False
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("测试结果统计:")
    print(f"日志消息数量: {len(log_messages)}")
    print(f"进度更新数量: {len(progress_updates)}")
    print(f"训练器状态: {'运行中' if trainer.is_training else '已停止'}")
    
    # 检查exp文件夹
    runs_dir = Path("runs/train")
    if runs_dir.exists():
        exp_dirs = list(runs_dir.glob("exp*"))
        if exp_dirs:
            latest_exp = max(exp_dirs, key=lambda x: x.stat().st_mtime)
            print(f"✓ 最新训练文件夹: {latest_exp}")
            
            # 检查训练结果文件
            results_files = [
                "weights/best.pt",
                "weights/last.pt",
                "results.png",
                "confusion_matrix.png"
            ]
            
            for file_name in results_files:
                file_path = latest_exp / file_name
                if file_path.exists():
                    print(f"  ✓ {file_name}")
                else:
                    print(f"  ❌ {file_name}")
        else:
            print("❌ 未找到exp文件夹")
    else:
        print("❌ runs/train目录不存在")
    
    # 清理测试文件
    if os.path.exists(data_config_path):
        os.remove(data_config_path)
        print(f"✓ 清理测试文件: {data_config_path}")
    
    print("=" * 60)
    print("简化训练功能测试完成")
    print("优点：")
    print("- 代码简洁，稳定性高")
    print("- 没有复杂的监控逻辑")
    print("- 减少了潜在的错误源")
    print("缺点：")
    print("- 没有实时进度显示")
    print("- 无法查看训练过程中的指标")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    test_simple_training()
