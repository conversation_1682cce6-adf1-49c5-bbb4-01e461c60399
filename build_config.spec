# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 获取项目根目录
project_root = Path(os.getcwd())

# 数据文件和隐藏导入
datas = []
hiddenimports = [
    # 标准库模块
    'unittest',
    'unittest.mock',
    'collections',
    'collections.abc',
    'importlib',
    'importlib.util',
    'importlib.metadata',
    'typing',
    'typing_extensions',

    # PySide6相关
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'PySide6.QtOpenGL',
    'PySide6.QtOpenGLWidgets',

    # PyTorch相关
    'torch',
    'torch.nn',
    'torch.nn.functional',
    'torch.fx',
    'torch.fx.passes',
    'torch.fx.passes.shape_prop',
    'torch._dispatch',
    'torch._dispatch.python',
    'torch.export',
    'torchvision',
    'torchvision.transforms',
    
    # OpenCV相关
    'cv2',
    'cv2.dnn',
    
    # Ultralytics相关
    'ultralytics',
    'ultralytics.models',
    'ultralytics.models.yolo',
    'ultralytics.models.yolo.detect',
    'ultralytics.models.yolo.segment',
    'ultralytics.models.yolo.pose',
    'ultralytics.models.yolo.classify',
    'ultralytics.models.yolo.obb',
    'ultralytics.engine',
    'ultralytics.engine.predictor',
    'ultralytics.engine.results',
    'ultralytics.utils',
    'ultralytics.utils.plotting',
    'ultralytics.data',
    'ultralytics.data.utils',
    
    # ONNX相关
    'onnx',
    'onnxruntime',
    'onnxruntime.capi',
    'onnxruntime.capi.onnxruntime_pybind11_state',
    
    # OpenVINO相关
    'openvino',
    'openvino.runtime',
    'openvino.inference_engine',
    
    # NCNN相关
    'ncnn',
    
    # 其他依赖
    'numpy',
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'pathlib',
    'yaml',
    'matplotlib',
    'matplotlib.pyplot',
    'seaborn',
    'pandas',
    'scipy',
    'tqdm',
    'requests',
    'urllib3',
    
    # 项目模块
    'core',
    'core.detector',
    'core.model_converter', 
    'core.utils',
    'ui',
    'ui.main_window',
    'ui.widgets',
]

# 排除的模块
excludes = [
    'tkinter',
    'unittest',
    'test',
    'tests',
    'pytest',
    'IPython',
    'jupyter',
    'notebook',
]

# 二进制文件
binaries = []

# 分析主程序
a = Analysis(
    ['main.py'],
    pathex=[str(project_root)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 收集所有文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='YOLO目标检测系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
