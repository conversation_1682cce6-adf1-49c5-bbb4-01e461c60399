# 📁 YOLO模型文件目录

## 📖 说明
此目录用于存放YOLO预训练模型文件。由于网络连接问题，您需要手动下载模型文件并放置在此目录中。

## 🔽 模型下载

### 检测模型（Detection）
| 模型名称 | 文件大小 | 下载链接 | 说明 |
|---------|---------|----------|------|
| YOLOv8n | ~6MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n.pt) | 最小模型，速度最快 |
| YOLOv8s | ~22MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s.pt) | 小模型，平衡速度和精度 |
| YOLOv8m | ~52MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8m.pt) | 中等模型 |
| YOLOv8l | ~88MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8l.pt) | 大模型 |
| YOLOv8x | ~136MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8x.pt) | 超大模型，精度最高 |

### 分割模型（Segmentation）
| 模型名称 | 文件大小 | 下载链接 | 说明 |
|---------|---------|----------|------|
| YOLOv8n-seg | ~7MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n-seg.pt) | 最小分割模型 |
| YOLOv8s-seg | ~24MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s-seg.pt) | 小分割模型 |
| YOLOv8m-seg | ~54MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8m-seg.pt) | 中等分割模型 |

### 姿态估计模型（Pose Estimation）
| 模型名称 | 文件大小 | 下载链接 | 说明 |
|---------|---------|----------|------|
| YOLOv8n-pose | ~7MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n-pose.pt) | 最小姿态估计模型 |
| YOLOv8s-pose | ~24MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s-pose.pt) | 小姿态估计模型 |

### 分类模型（Classification）
| 模型名称 | 文件大小 | 下载链接 | 说明 |
|---------|---------|----------|------|
| YOLOv8n-cls | ~6MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n-cls.pt) | 最小分类模型 |
| YOLOv8s-cls | ~22MB | [下载](https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s-cls.pt) | 小分类模型 |

## 📥 下载步骤

1. **选择模型**：根据您的需求选择合适的模型
   - 如果追求速度：选择 `n` 版本
   - 如果追求精度：选择 `l` 或 `x` 版本
   - 如果平衡考虑：选择 `s` 或 `m` 版本

2. **下载文件**：
   - 点击上表中的下载链接
   - 或复制链接到浏览器/下载工具中下载
   - 确保文件完整下载（检查文件大小）

3. **放置文件**：
   - 将下载的 `.pt` 文件放入此目录
   - 保持原始文件名（如 `yolov8n.pt`）

## 🔧 使用方法

### 方法1：在训练界面中使用
1. 启动程序：`python main.py`
2. 切换到"模型训练"标签页
3. 在"模型选择"部分点击"浏览"
4. 选择此目录中的模型文件
5. 点击"加载自定义模型"

### 方法2：直接在代码中使用
```python
from ultralytics import YOLO

# 加载本地模型
model = YOLO('models/yolov8n.pt')
print("模型加载成功！")
```

## 📋 文件检查清单

下载完成后，此目录应包含：
```
models/
├── README.md              # 本说明文件
├── yolov8n.pt            # 推荐：最小检测模型
├── yolov8s.pt            # 可选：小检测模型
├── yolov8n-seg.pt        # 可选：分割模型
└── ...                   # 其他模型文件
```

## ⚠️ 注意事项

1. **文件完整性**：确保下载的文件大小与表格中的大小匹配
2. **文件名称**：保持原始文件名，不要重命名
3. **文件格式**：只支持 `.pt` 和 `.pth` 格式的PyTorch模型
4. **存储空间**：确保有足够的磁盘空间存储模型文件

## 🚀 推荐下载

对于初次使用，推荐下载以下模型：

1. **YOLOv8n.pt** - 用于快速测试和学习
2. **YOLOv8s.pt** - 用于实际项目开发
3. **YOLOv8n-seg.pt** - 如果需要分割功能

## 🔍 验证模型

下载完成后，可以运行以下命令验证模型：

```python
python -c "from ultralytics import YOLO; model = YOLO('models/yolov8n.pt'); print('模型验证成功！')"
```

## 📞 获取帮助

如果遇到问题：
1. 检查文件是否完整下载
2. 确认文件路径正确
3. 查看 `离线模型使用指南.md` 获取更多帮助
4. 确保Python环境和依赖包正确安装

---

**下载完成后即可开始使用YOLO模型训练功能！🎉**
