#!/usr/bin/env python3
"""
测试全局终端监控功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_global_monitoring():
    """测试全局终端监控功能"""
    print("=" * 60)
    print("测试全局终端监控功能")
    print("=" * 60)
    
    # 捕获的日志
    captured_logs = []
    
    def log_capture(message):
        """日志捕获回调"""
        captured_logs.append(message)
        print(f"[CAPTURED] {message}")
    
    try:
        from ui.widgets.training_widget import GlobalTerminalCapture
        
        # 获取全局捕获器实例
        global_capture = GlobalTerminalCapture.get_instance()
        
        print("开始测试全局终端监控...")
        
        # 添加回调
        global_capture.add_callback(log_capture)
        
        # 启动捕获
        global_capture.start_capture()
        
        print("全局终端监控已启动，开始测试输出...")
        
        # 测试各种输出
        print("这是第一条测试消息")
        print("这是第二条测试消息")
        
        # 测试YOLO风格的输出
        print("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
        print("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
        
        # 测试表头
        print("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 测试进度行
        print("      1/100      1.05G      1.523      3.141      4.765      1.325         22        640:   0%|          | 0/96 [00:39<00:00,  2.40it/s]")
        print("      1/100      1.05G      1.523      3.141      4.765      1.325         22        640:  50%|#####     | 48/96 [00:20<00:20,  2.40it/s]")
        print("      1/100      1.05G      1.523      3.141      4.765      1.325         22        640: 100%|##########| 96/96 [00:39<00:00,  2.40it/s]")
        
        # 测试验证输出
        print("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:00<00:00,  3.20it/s]")
        print("                   all         24        196     0.0662     0.0864     0.0428     0.0302     0.0116     0.0451    0.00739    0.00122")
        
        # 测试错误输出
        import sys
        sys.stderr.write("这是一条错误消息\n")
        sys.stderr.flush()
        
        # 等待一下确保所有输出都被捕获
        time.sleep(0.5)
        
        # 移除回调
        global_capture.remove_callback(log_capture)
        
        print("\n" + "=" * 60)
        print("全局终端监控测试结果")
        print("=" * 60)
        
        print(f"捕获的日志条数: {len(captured_logs)}")
        
        print("\n捕获的日志内容:")
        for i, log in enumerate(captured_logs):
            print(f"  {i+1:2d}. {log}")
        
        # 验证关键内容
        has_test_messages = any('测试消息' in log for log in captured_logs)
        has_yolo_info = any('Ultralytics YOLOv8' in log for log in captured_logs)
        has_model_summary = any('Model summary' in log for log in captured_logs)
        has_epoch_header = any('Epoch    GPU_mem' in log for log in captured_logs)
        has_progress_lines = len([log for log in captured_logs if '%|' in log and 'it/s' in log])
        has_validation_progress = any('Class     Images' in log and '%|' in log for log in captured_logs)
        has_validation_result = any('all         24' in log for log in captured_logs)
        has_error_message = any('错误消息' in log for log in captured_logs)
        
        print(f"\n内容验证:")
        print(f"✓ 测试消息: {'是' if has_test_messages else '否'}")
        print(f"✓ YOLO信息: {'是' if has_yolo_info else '否'}")
        print(f"✓ 模型摘要: {'是' if has_model_summary else '否'}")
        print(f"✓ Epoch表头: {'是' if has_epoch_header else '否'}")
        print(f"✓ 进度行数: {has_progress_lines}")
        print(f"✓ 验证进度: {'是' if has_validation_progress else '否'}")
        print(f"✓ 验证结果: {'是' if has_validation_result else '否'}")
        print(f"✓ 错误消息: {'是' if has_error_message else '否'}")
        
        # 总体评估
        success = (has_test_messages and has_yolo_info and has_model_summary and 
                  has_epoch_header and has_progress_lines >= 3 and 
                  has_validation_progress and has_validation_result and has_error_message)
        
        print(f"\n测试结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print("🎉 全局终端监控功能完美工作！")
            print("现在从程序启动开始，所有终端输出都会被一字不差地复制到GUI中。")
            print("包括：")
            print("- 程序启动信息")
            print("- YOLO训练输出")
            print("- 错误消息")
            print("- 所有print输出")
        else:
            print("⚠️ 全局终端监控功能需要进一步调试。")
        
        return success
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n" + "=" * 60)
    print("测试GUI集成")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("创建训练组件...")
        
        # 创建训练组件（这会自动启动全局监控）
        widget = TrainingWidget()
        
        print("训练组件已创建，全局监控应该已启动")
        
        # 测试一些输出
        print("测试GUI集成输出1")
        print("测试GUI集成输出2")
        print("测试GUI集成输出3")
        
        # 等待一下
        time.sleep(0.5)
        
        # 检查GUI中的日志
        log_text = widget.log_text.toPlainText()
        lines = log_text.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        print(f"\nGUI中的日志行数: {len(non_empty_lines)}")
        print("GUI中的日志内容:")
        for i, line in enumerate(non_empty_lines[-10:]):  # 显示最后10行
            print(f"  {i+1:2d}. {line}")
        
        # 检查是否包含测试输出
        has_gui_test = any('测试GUI集成输出' in line for line in non_empty_lines)
        has_startup_msg = any('YOLO训练系统已启动' in line for line in non_empty_lines)
        
        print(f"\nGUI集成验证:")
        print(f"✓ 包含测试输出: {'是' if has_gui_test else '否'}")
        print(f"✓ 包含启动消息: {'是' if has_startup_msg else '否'}")
        
        success = has_gui_test and has_startup_msg
        
        print(f"\nGUI集成测试: {'✅ 成功' if success else '❌ 失败'}")
        
        return success
        
    except Exception as e:
        print(f"GUI集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试全局终端监控功能...")
    
    # 测试全局监控
    monitoring_ok = test_global_monitoring()
    
    # 测试GUI集成
    gui_ok = test_gui_integration()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"全局监控: {'✅ 通过' if monitoring_ok else '❌ 失败'}")
    print(f"GUI集成: {'✅ 通过' if gui_ok else '❌ 失败'}")
    
    if monitoring_ok and gui_ok:
        print("\n🎉 所有测试通过！")
        print("全局终端监控功能已经完美实现！")
        print("现在启动主程序，从启动开始的所有终端输出都会显示在GUI中。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")
    
    print("=" * 60)
