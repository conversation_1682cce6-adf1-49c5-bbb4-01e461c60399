#!/usr/bin/env python3
"""
测试表头保留功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_header_preservation():
    """测试表头保留功能"""
    print("=" * 60)
    print("测试表头保留功能")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练组件
        widget = TrainingWidget()
        
        print("开始测试表头保留...")
        
        # 模拟完整的YOLO训练输出序列
        
        # 1. 基本信息
        widget.add_log("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
        widget.add_log("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
        print("✓ 添加了基本信息")
        
        # 2. 训练表头
        widget.add_log("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        print("✓ 添加了训练表头")
        
        # 3. 训练进度（动态更新）
        base_line = "      1/100     0.975G      1.455      3.108       4.79      1.317         55        640: "
        
        for i in range(0, 101, 25):
            progress_bar = f"{i:3d}%|{'#' * (i//10)}{'.' * (10-i//10)}| {i}/96 [00:14<00:27,  2.26it/s]"
            full_line = base_line + progress_bar
            
            widget.add_log(full_line)
            print(f"✓ 更新训练进度: {i}%")
            time.sleep(0.1)
        
        # 4. 验证表头（不包含进度条）
        widget.add_log("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95)")
        print("✓ 添加了验证表头")
        
        # 5. 验证进度（动态更新）
        val_base = "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): "
        val_progress = "100%|##########| 3/3 [00:01<00:00,  2.99it/s]"
        widget.add_log(val_base + val_progress)
        print("✓ 添加了验证进度")
        
        # 6. 验证结果
        widget.add_log("                   all         24        196     0.0662     0.0864     0.0428     0.0302     0.0116     0.0451    0.00739    0.00122")
        print("✓ 添加了验证结果")
        
        # 检查最终结果
        final_text = widget.log_text.toPlainText()
        lines = final_text.split('\n')
        
        print(f"\n最终日志行数: {len(lines)}")
        print("完整日志内容:")
        for i, line in enumerate(lines):
            if line.strip():
                print(f"  {i+1:2d}. {line}")
        
        # 检查关键内容
        has_basic_info = any('Ultralytics YOLOv8' in line for line in lines)
        has_model_summary = any('Model summary' in line for line in lines)
        has_train_header = any('Epoch    GPU_mem   box_loss' in line for line in lines)
        has_train_progress = any('1/100' in line and '%|' in line and 'it/s' in line for line in lines)
        has_val_header = any('Class     Images  Instances' in line and '%|' not in line for line in lines)
        has_val_progress = any('Class     Images  Instances' in line and '%|' in line and 'it/s' in line for line in lines)
        has_val_results = any('all         24        196' in line for line in lines)
        
        print(f"\n内容验证:")
        print(f"✓ 基本信息: {'是' if has_basic_info else '否'}")
        print(f"✓ 模型摘要: {'是' if has_model_summary else '否'}")
        print(f"✓ 训练表头: {'是' if has_train_header else '否'}")
        print(f"✓ 训练进度: {'是' if has_train_progress else '否'}")
        print(f"✓ 验证表头: {'是' if has_val_header else '否'}")
        print(f"✓ 验证进度: {'是' if has_val_progress else '否'}")
        print(f"✓ 验证结果: {'是' if has_val_results else '否'}")
        
        # 检查行数是否合理（应该是7行：基本信息2行 + 训练表头1行 + 训练进度1行 + 验证表头1行 + 验证进度1行 + 验证结果1行）
        expected_lines = 7
        actual_lines = len([line for line in lines if line.strip()])
        
        print(f"\n行数检查:")
        print(f"期望行数: {expected_lines}")
        print(f"实际行数: {actual_lines}")
        print(f"行数合理: {'是' if actual_lines <= expected_lines + 1 else '否'}")  # 允许1行误差
        
        # 总体评估
        all_content_present = all([has_basic_info, has_model_summary, has_train_header, 
                                 has_train_progress, has_val_header, has_val_progress, has_val_results])
        reasonable_line_count = actual_lines <= expected_lines + 1
        
        success = all_content_present and reasonable_line_count
        
        print(f"\n测试结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print("🎉 表头保留功能完美工作！")
            print("现在您将看到完整的YOLO训练输出，包括：")
            print("- 保留的训练表头")
            print("- 动态更新的训练进度")
            print("- 保留的验证表头")
            print("- 动态更新的验证进度")
            print("- 完整的验证结果")
        else:
            print("⚠️ 表头保留功能需要进一步调试。")
        
        return success
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试表头保留功能...")
    
    success = test_header_preservation()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 表头保留功能测试通过！")
        print("现在YOLO训练将显示：")
        print("1. 完整的基本信息")
        print("2. 保留的训练表头")
        print("3. 动态更新的训练进度（不重复）")
        print("4. 保留的验证表头")
        print("5. 动态更新的验证进度（不重复）")
        print("6. 完整的验证结果")
        print("\n您的问题已经完全解决！")
    else:
        print("❌ 测试失败，需要进一步调试。")
    
    print("=" * 60)
