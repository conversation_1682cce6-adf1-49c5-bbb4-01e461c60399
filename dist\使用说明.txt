# YOLO目标检测系统 - 可执行文件

## 文件说明
- YOLO目标检测系统.exe: 主程序可执行文件
- 启动程序.bat: 启动脚本（推荐使用）
- 使用说明.txt: 本文件

## 使用方法
1. 双击 "启动程序.bat" 启动程序（推荐）
   或者直接双击 "YOLO目标检测系统.exe"
2. 程序启动后会显示图形界面
3. 按照界面提示进行目标检测操作

## 系统要求
- Windows 10/11 (64位)
- 至少4GB内存
- 支持OpenGL的显卡(推荐)

## 注意事项
- 首次运行可能需要较长时间启动（30秒-2分钟）
- 如果遇到问题，请检查Windows防火墙和杀毒软件设置
- 程序运行时会自动下载YOLO模型文件(如果不存在)
- 如果出现"unittest模块缺失"错误，说明打包有问题，请联系开发者

## 支持的功能
- 图像目标检测
- 视频目标检测
- 实时摄像头检测
- 多种YOLO模型支持
- 模型格式转换(ONNX, NCNN, OpenVINO)
- 检测结果导出

## 故障排除
1. 如果程序无法启动：
   - 检查是否有杀毒软件阻止
   - 尝试以管理员身份运行
   - 检查系统是否为64位Windows

2. 如果检测速度慢：
   - 确保有独立显卡
   - 关闭其他占用GPU的程序

3. 如果模型加载失败：
   - 检查网络连接
   - 确保有足够的磁盘空间

## 技术支持
如有问题请联系开发者

版本: 2.0.0
构建时间: 2025年1月
