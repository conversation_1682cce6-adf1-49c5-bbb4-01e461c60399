#!/usr/bin/env python3
"""
调试YOLO输出格式
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def debug_yolo_output():
    """调试YOLO输出格式"""
    print("=" * 60)
    print("调试YOLO输出格式")
    print("=" * 60)
    
    # 捕获的原始输出
    raw_outputs = []
    
    def raw_capture(text):
        """原始输出捕获"""
        raw_outputs.append(repr(text))  # 使用repr显示转义字符
        sys.__stdout__.write(f"RAW: {repr(text)}\n")
        sys.__stdout__.flush()
    
    # 创建简单的捕获器
    class SimpleCapture:
        def __init__(self, callback):
            self.callback = callback
            self.original_stdout = None
            
        def start_capture(self):
            import sys
            self.original_stdout = sys.stdout
            sys.stdout = self
            
        def stop_capture(self):
            import sys
            if self.original_stdout:
                sys.stdout = self.original_stdout
                
        def write(self, text):
            if self.original_stdout:
                self.original_stdout.write(text)
                self.original_stdout.flush()
            
            if text and self.callback:
                self.callback(text)
            
            return len(text)
        
        def flush(self):
            if self.original_stdout:
                self.original_stdout.flush()
    
    capture = SimpleCapture(raw_capture)
    
    print("开始捕获YOLO风格的输出...")
    
    try:
        capture.start_capture()
        
        # 模拟YOLO的实际输出模式
        print("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 模拟进度条输出（使用\r回车符）
        base_line = "      1/100      0.85G      1.919      3.481      4.364      1.791         27        640: "
        
        for i in range(0, 101, 10):
            progress_bar = f"{i:3d}%|{'#' * (i//10)}{'.' * (10-i//10)}| {i}/100"
            full_line = base_line + progress_bar + " [00:30<00:10,  2.35it/s]"
            
            # 使用\r来覆盖当前行
            print(f"\r{full_line}", end='', flush=True)
            time.sleep(0.2)
        
        # 最后换行
        print()
        
        # 验证阶段
        print("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:01<00:00,  2.99it/s]")
        print("                   all         24        196     0.0496      0.503      0.106      0.076     0.0462      0.427     0.0961     0.0534")
        
    finally:
        capture.stop_capture()
    
    # 分析捕获的输出
    print("\n" + "=" * 60)
    print("捕获的原始输出分析:")
    print("=" * 60)
    
    for i, output in enumerate(raw_outputs):
        print(f"{i+1:2d}. {output}")
    
    # 分析回车符和换行符
    has_carriage_return = any('\\r' in output for output in raw_outputs)
    has_newline = any('\\n' in output for output in raw_outputs)
    
    print(f"\n输出特征分析:")
    print(f"包含回车符 (\\r): {has_carriage_return}")
    print(f"包含换行符 (\\n): {has_newline}")
    
    # 找出进度条相关的输出
    progress_outputs = [output for output in raw_outputs if '%|' in output or 'it/s' in output]
    print(f"进度条相关输出数量: {len(progress_outputs)}")
    
    if progress_outputs:
        print("进度条输出示例:")
        for i, output in enumerate(progress_outputs[:3]):
            print(f"  {i+1}. {output}")
    
    print("=" * 60)
    print("调试完成")
    print("=" * 60)
    
    return raw_outputs

if __name__ == "__main__":
    outputs = debug_yolo_output()
    
    print("\n分析结果:")
    print("- 如果看到\\r字符，说明YOLO使用回车符进行动态更新")
    print("- 如果看到\\n字符，说明YOLO使用换行符创建新行")
    print("- 我们需要根据这些字符来正确处理动态更新")
