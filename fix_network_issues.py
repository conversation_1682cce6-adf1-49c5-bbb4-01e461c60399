#!/usr/bin/env python3
"""
网络问题解决方案脚本
帮助解决模型下载时的网络连接问题
"""

import os
import sys
import requests
from pathlib import Path
import urllib3
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
urllib3.disable_warnings(InsecureRequestWarning)

def check_network_connectivity():
    """检查网络连接"""
    print("=" * 50)
    print("检查网络连接...")
    print("=" * 50)
    
    test_urls = [
        "https://www.baidu.com",
        "https://github.com",
        "https://huggingface.co"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10, verify=False)
            if response.status_code == 200:
                print(f"✓ {url} - 连接正常")
            else:
                print(f"⚠ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"✗ {url} - 连接失败: {str(e)}")

def configure_proxy_settings():
    """配置代理设置"""
    print("\n" + "=" * 50)
    print("配置代理设置...")
    print("=" * 50)
    
    # 清除可能的代理环境变量
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    
    for var in proxy_vars:
        if var in os.environ:
            print(f"清除环境变量: {var} = {os.environ[var]}")
            del os.environ[var]
        else:
            print(f"环境变量 {var} 未设置")
    
    # 设置requests不使用代理
    os.environ['NO_PROXY'] = '*'
    print("✓ 已设置 NO_PROXY=*")

def download_model_manually(model_name="yolov8n"):
    """手动下载模型"""
    print(f"\n" + "=" * 50)
    print(f"尝试手动下载模型: {model_name}")
    print("=" * 50)
    
    # 模型URL映射
    model_urls = {
        'yolov8n': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n.pt',
        'yolov8s': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s.pt',
        'yolov8m': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8m.pt',
        'yolov8n-seg': 'https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n-seg.pt',
    }
    
    if model_name not in model_urls:
        print(f"✗ 不支持的模型: {model_name}")
        return False
    
    url = model_urls[model_name]
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    model_file = models_dir / f"{model_name}.pt"
    
    if model_file.exists():
        print(f"✓ 模型已存在: {model_file}")
        return True
    
    try:
        print(f"开始下载: {url}")
        
        # 配置session
        session = requests.Session()
        session.trust_env = False
        session.verify = False  # 忽略SSL验证
        
        # 设置headers
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = session.get(url, stream=True, headers=headers, timeout=60)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0
        
        print(f"文件大小: {total_size / (1024*1024):.1f} MB")
        
        with open(model_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{total_size})", end='')
        
        print(f"\n✓ 下载完成: {model_file}")
        return True
        
    except Exception as e:
        print(f"\n✗ 下载失败: {str(e)}")
        if model_file.exists():
            model_file.unlink()  # 删除不完整的文件
        return False

def use_ultralytics_auto_download():
    """使用ultralytics自动下载"""
    print("\n" + "=" * 50)
    print("尝试使用ultralytics自动下载...")
    print("=" * 50)
    
    try:
        from ultralytics import YOLO
        
        # 尝试加载模型（会自动下载）
        print("正在加载 yolov8n.pt...")
        model = YOLO('yolov8n.pt')
        
        if model.model is not None:
            print("✓ 模型加载成功")
            
            # 检查模型文件位置
            default_path = Path.home() / '.ultralytics' / 'models' / 'yolov8n.pt'
            if default_path.exists():
                print(f"✓ 模型文件位置: {default_path}")
                
                # 复制到本地models目录
                models_dir = Path("models")
                models_dir.mkdir(exist_ok=True)
                local_path = models_dir / 'yolov8n.pt'
                
                import shutil
                shutil.copy2(default_path, local_path)
                print(f"✓ 已复制到: {local_path}")
                
            return True
        else:
            print("✗ 模型加载失败")
            return False
            
    except Exception as e:
        print(f"✗ 自动下载失败: {str(e)}")
        return False

def create_offline_solution():
    """创建离线解决方案"""
    print("\n" + "=" * 50)
    print("创建离线解决方案...")
    print("=" * 50)
    
    # 创建离线使用说明
    offline_guide = """# 离线模型使用指南

## 问题描述
由于网络连接问题，无法自动下载预训练模型。

## 解决方案

### 方案1：手动下载模型
1. 使用浏览器或下载工具手动下载模型文件
2. 将下载的模型文件放入 `models/` 目录
3. 在训练界面中选择"加载自定义模型"

### 常用模型下载链接：
- YOLOv8n: https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n.pt
- YOLOv8s: https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s.pt
- YOLOv8m: https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8m.pt
- YOLOv8n-seg: https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n-seg.pt

### 方案2：使用镜像源
如果您在中国大陆，可以尝试使用以下镜像源：
1. 设置环境变量：
   ```
   set HF_ENDPOINT=https://hf-mirror.com
   ```
2. 或使用清华大学镜像等

### 方案3：配置代理
如果您使用代理，请确保：
1. 代理设置正确
2. 代理支持HTTPS连接
3. 防火墙允许Python访问网络

## 使用步骤
1. 将模型文件放入 `models/` 目录
2. 启动程序：`python main.py`
3. 切换到"模型训练"标签页
4. 点击"浏览"选择本地模型文件
5. 点击"加载自定义模型"
"""
    
    with open("离线模型使用指南.md", 'w', encoding='utf-8') as f:
        f.write(offline_guide)
    
    print("✓ 已创建离线使用指南: 离线模型使用指南.md")
    
    # 创建models目录
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    print(f"✓ 已创建模型目录: {models_dir}")
    
    # 创建示例模型信息文件
    model_info = """# 模型文件说明

请将下载的YOLO模型文件放在此目录中。

支持的模型格式：
- .pt (PyTorch模型)
- .pth (PyTorch模型)

常用模型：
- yolov8n.pt (最小模型，速度快)
- yolov8s.pt (小模型)
- yolov8m.pt (中等模型)
- yolov8l.pt (大模型)
- yolov8x.pt (超大模型，精度高)

分割模型：
- yolov8n-seg.pt
- yolov8s-seg.pt
- 等等...

使用方法：
1. 将模型文件复制到此目录
2. 在训练界面中选择"加载自定义模型"
3. 浏览并选择模型文件
"""
    
    with open(models_dir / "README.md", 'w', encoding='utf-8') as f:
        f.write(model_info)
    
    print(f"✓ 已创建模型说明: {models_dir / 'README.md'}")

def main():
    """主函数"""
    print("YOLO模型下载网络问题解决方案")
    print("=" * 50)
    
    # 1. 检查网络连接
    check_network_connectivity()
    
    # 2. 配置代理设置
    configure_proxy_settings()
    
    # 3. 尝试ultralytics自动下载
    if use_ultralytics_auto_download():
        print("\n🎉 问题已解决！模型下载成功。")
        return
    
    # 4. 尝试手动下载
    if download_model_manually("yolov8n"):
        print("\n🎉 问题已解决！模型手动下载成功。")
        return
    
    # 5. 创建离线解决方案
    create_offline_solution()
    
    print("\n" + "=" * 50)
    print("网络问题解决方案")
    print("=" * 50)
    print("由于网络连接问题，自动下载失败。")
    print("请参考以下解决方案：")
    print()
    print("1. 📁 查看 '离线模型使用指南.md' 获取详细说明")
    print("2. 🌐 手动下载模型文件到 'models/' 目录")
    print("3. 🔧 在训练界面中使用'加载自定义模型'功能")
    print("4. ⚙️ 检查网络代理设置")
    print()
    print("常用模型下载地址：")
    print("- YOLOv8n: https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8n.pt")
    print("- YOLOv8s: https://github.com/ultralytics/assets/releases/download/v8.2.0/yolov8s.pt")
    print()
    print("下载后将文件放入 'models/' 目录即可使用。")

if __name__ == "__main__":
    main()
