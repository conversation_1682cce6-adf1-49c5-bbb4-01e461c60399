#!/usr/bin/env python3
"""
测试修复后的动态更新功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_fixed_dynamic():
    """测试修复后的动态更新功能"""
    print("=" * 60)
    print("测试修复后的动态更新功能")
    print("=" * 60)
    
    # 捕获的日志
    static_logs = []
    dynamic_logs = []
    
    def static_log_capture(message):
        """静态日志捕获（新行）"""
        static_logs.append(message)
        print(f"[STATIC] {message}")
    
    def dynamic_log_capture(message):
        """动态日志捕获（覆盖最后一行）"""
        dynamic_logs.append(message)
        print(f"[DYNAMIC] {message}")
    
    # 创建修复后的终端捕获器
    from ui.widgets.training_widget import TerminalCapture
    terminal_capture = TerminalCapture(static_log_capture, dynamic_log_capture)
    
    print("开始测试修复后的功能...")
    
    try:
        # 开始捕获
        terminal_capture.start_capture()
        
        # 模拟YOLO的实际输出模式
        
        # 1. 静态标题行（以\n结尾）
        print("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 2. 动态进度行（以\r开头）
        base_line = "      1/100      0.85G      1.919      3.481      4.364      1.791         27        640: "
        
        for i in range(0, 101, 20):
            progress_bar = f"{i:3d}%|{'#' * (i//10)}{'.' * (10-i//10)}| {i}/100"
            full_line = base_line + progress_bar + " [00:30<00:10,  2.35it/s]"
            
            # 使用\r来覆盖当前行（模拟YOLO的实际行为）
            print(f"\r{full_line}", end='', flush=True)
            time.sleep(0.3)
        
        # 3. 换行符结束动态行
        print()
        
        # 4. 验证阶段静态行
        print("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:01<00:00,  2.99it/s]")
        print("                   all         24        196     0.0496      0.503      0.106      0.076     0.0462      0.427     0.0961     0.0534")
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止捕获
        terminal_capture.stop_capture()
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("修复后的测试结果:")
    print(f"静态日志条数: {len(static_logs)}")
    print(f"动态日志条数: {len(dynamic_logs)}")
    
    # 显示静态日志
    print("\n静态日志内容:")
    for i, log in enumerate(static_logs):
        print(f"  {i+1:2d}. {log}")
    
    # 显示动态日志
    print("\n动态日志内容:")
    for i, log in enumerate(dynamic_logs):
        print(f"  {i+1:2d}. {log}")
    
    # 检查期望的行为
    expected_static = 4  # 标题行 + 最终进度行 + 验证行 + 结果行
    expected_dynamic = 6  # 6次进度更新
    
    static_ok = len(static_logs) == expected_static
    dynamic_ok = len(dynamic_logs) == expected_dynamic
    
    # 检查是否有重复的进度行在静态日志中
    progress_in_static = any('1/100' in log and '%|' in log for log in static_logs)
    
    print(f"\n功能验证:")
    print(f"✓ 静态日志数量正确: {'成功' if static_ok else '失败'} (期望{expected_static}, 实际{len(static_logs)})")
    print(f"✓ 动态日志数量正确: {'成功' if dynamic_ok else '失败'} (期望{expected_dynamic}, 实际{len(dynamic_logs)})")
    print(f"✓ 进度行不重复出现在静态日志: {'成功' if not progress_in_static else '失败'}")
    
    # 检查最终静态日志是否包含完成的进度行
    final_progress_in_static = any('100%|##########|' in log for log in static_logs)
    print(f"✓ 最终进度行正确转为静态: {'成功' if final_progress_in_static else '失败'}")
    
    print("=" * 60)
    print("修复后的动态更新功能测试完成")
    
    if static_ok and dynamic_ok and not progress_in_static and final_progress_in_static:
        print("🎉 所有测试通过！动态更新功能已正确修复！")
        print("现在YOLO训练将正确显示：")
        print("- 标题行：静态显示")
        print("- 进度条：动态更新（不创建新行）")
        print("- 完成后：进度条转为静态行")
        print("- 结果行：静态显示")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
    
    print("=" * 60)
    
    return static_ok and dynamic_ok and not progress_in_static

if __name__ == "__main__":
    success = test_fixed_dynamic()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    
    if success:
        print("\n✅ 动态更新功能修复成功！")
        print("现在可以重新启动训练，应该会看到正确的动态进度条效果。")
    else:
        print("\n❌ 还需要进一步调试和修复。")
