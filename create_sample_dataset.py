#!/usr/bin/env python3
"""
创建示例数据集脚本
用于演示YOLO训练功能的数据集格式
"""

import os
import yaml
from pathlib import Path


def create_sample_dataset():
    """创建示例数据集结构"""
    
    # 数据集根目录
    dataset_root = Path("sample_dataset")
    
    # 创建目录结构
    dirs = [
        dataset_root / "train" / "images",
        dataset_root / "train" / "labels", 
        dataset_root / "val" / "images",
        dataset_root / "val" / "labels"
    ]
    
    for dir_path in dirs:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {dir_path}")
    
    # 创建示例标签文件
    sample_labels = {
        "train": [
            "train_img1.txt",
            "train_img2.txt", 
            "train_img3.txt"
        ],
        "val": [
            "val_img1.txt",
            "val_img2.txt"
        ]
    }
    
    # 示例标签内容（假设有3个类别：person, car, bike）
    sample_annotations = [
        "0 0.5 0.5 0.3 0.4",  # person in center
        "1 0.2 0.3 0.15 0.25",  # car on left
        "2 0.8 0.7 0.1 0.2"   # bike on right
    ]
    
    # 创建训练集标签文件
    for label_file in sample_labels["train"]:
        label_path = dataset_root / "train" / "labels" / label_file
        with open(label_path, 'w') as f:
            # 随机选择1-3个标注
            import random
            num_objects = random.randint(1, 3)
            selected_annotations = random.sample(sample_annotations, num_objects)
            f.write('\n'.join(selected_annotations))
        print(f"创建标签文件: {label_path}")
    
    # 创建验证集标签文件
    for label_file in sample_labels["val"]:
        label_path = dataset_root / "val" / "labels" / label_file
        with open(label_path, 'w') as f:
            # 随机选择1-2个标注
            import random
            num_objects = random.randint(1, 2)
            selected_annotations = random.sample(sample_annotations, num_objects)
            f.write('\n'.join(selected_annotations))
        print(f"创建标签文件: {label_path}")
    
    # 创建占位图片文件说明
    image_info = {
        "train": ["train_img1.jpg", "train_img2.jpg", "train_img3.jpg"],
        "val": ["val_img1.jpg", "val_img2.jpg"]
    }
    
    for split in ["train", "val"]:
        for img_file in image_info[split]:
            img_path = dataset_root / split / "images" / img_file
            # 创建一个说明文件而不是真实图片
            readme_path = img_path.with_suffix('.txt')
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(f"这里应该放置图片文件: {img_file}\n")
                f.write("请将您的训练图片放在这个目录中\n")
                f.write("支持的格式: .jpg, .jpeg, .png, .bmp\n")
            print(f"创建图片说明: {readme_path}")
    
    # 创建data.yaml配置文件
    data_config = {
        'path': str(dataset_root.absolute()),
        'train': str((dataset_root / "train" / "images").absolute()),
        'val': str((dataset_root / "val" / "images").absolute()),
        'nc': 3,  # 类别数量
        'names': ['person', 'car', 'bike']  # 类别名称
    }
    
    config_path = dataset_root / "data.yaml"
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(data_config, f, default_flow_style=False, allow_unicode=True)
    print(f"创建配置文件: {config_path}")
    
    # 创建README文件
    readme_content = """# 示例数据集

这是一个YOLO训练的示例数据集结构。

## 目录结构
```
sample_dataset/
├── train/
│   ├── images/          # 训练图片目录
│   └── labels/          # 训练标签目录
├── val/
│   ├── images/          # 验证图片目录
│   └── labels/          # 验证标签目录
└── data.yaml           # 数据集配置文件
```

## 使用说明

1. 将您的训练图片放入 `train/images/` 目录
2. 将您的验证图片放入 `val/images/` 目录
3. 确保每张图片都有对应的标签文件
4. 在训练界面中选择 `sample_dataset` 目录
5. 点击"验证数据集"按钮

## 标签格式

每个标签文件包含该图片中所有目标的标注：
```
class_id center_x center_y width height
```

- class_id: 类别ID（从0开始）
- center_x, center_y: 目标中心点坐标（归一化，0-1）
- width, height: 目标宽度和高度（归一化，0-1）

## 类别说明

本示例数据集包含3个类别：
- 0: person（人）
- 1: car（汽车）
- 2: bike（自行车）

您可以根据需要修改 `data.yaml` 文件中的类别定义。
"""
    
    readme_path = dataset_root / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"创建说明文件: {readme_path}")
    
    print(f"\n✅ 示例数据集创建完成！")
    print(f"📁 数据集路径: {dataset_root.absolute()}")
    print(f"📝 请将您的图片文件放入相应的images目录中")
    print(f"🎯 然后在训练界面中选择此目录进行训练")


if __name__ == "__main__":
    create_sample_dataset()
