#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试终端输出解析的脚本
"""

import re

# 测试样本输出
sample_outputs = [
    "save_dir=runs\\train\\exp5, save_frames=False, save_json=False, save_period=1",
    "        1/2         0G       1.41      2.733      4.619      1.227         35        640: 100%|██████████| 64/64 [02:47<00:00,  2.61s/it]",
    "        2/2         0G      1.284      2.329      4.142      1.135         64        640: 100%|██████████| 64/64 [02:47<00:00,  2.61s/it]",
    "Results saved to runs\\train\\exp5",
    "train: Scanning C:\\Users\\<USER>\\Desktop\\vs\\datasets\\train\\labels.cache... 382 images, 0 backgrounds, 0 corrupt: 100%|██████████| 382/382 [00:00<?, ?it/s]"
]

def test_patterns():
    """测试正则表达式模式"""
    
    # 定义模式
    exp_pattern = re.compile(r'Results saved to (.*/runs/train/exp\d*)')
    save_dir_pattern = re.compile(r'save_dir=([^,\s]+)')
    
    epoch_pattern = re.compile(r'Epoch\s+(\d+)/(\d+)')
    yolo_epoch_pattern = re.compile(r'(\d+)/(\d+)')
    
    batch_pattern = re.compile(r'(\d+)%\|.*?\|\s*(\d+)/(\d+)')
    progress_bar_pattern = re.compile(r'(\d+)%\|[█▌▍▎▏ ]*\|\s*(\d+)/(\d+)')
    yolo_progress_pattern = re.compile(r'(\d+)%')
    
    print("=" * 60)
    print("测试正则表达式模式")
    print("=" * 60)
    
    for i, text in enumerate(sample_outputs):
        print(f"\n样本 {i+1}: {text}")
        print("-" * 40)
        
        # 测试exp文件夹检测
        exp_match = exp_pattern.search(text)
        if exp_match:
            print(f"✓ exp_pattern 匹配: {exp_match.group(1)}")
        
        save_dir_match = save_dir_pattern.search(text)
        if save_dir_match:
            print(f"✓ save_dir_pattern 匹配: {save_dir_match.group(1)}")
        
        # 测试轮次检测
        epoch_match = epoch_pattern.search(text)
        if epoch_match:
            print(f"✓ epoch_pattern 匹配: {epoch_match.group(1)}/{epoch_match.group(2)}")
        
        # 测试YOLO轮次检测
        if text.strip().startswith((' ', '\t')) and '/' in text and any(keyword in text for keyword in ['GPU_mem', 'box_loss', 'seg_loss']):
            print("✓ 检测到训练进度行")
            parts = text.strip().split()
            for part in parts:
                if '/' in part and part.replace('/', '').replace('.', '').isdigit():
                    try:
                        current, total = part.split('/')
                        print(f"✓ 轮次信息: {current}/{total}")
                        break
                    except:
                        continue
        
        # 测试批次进度检测
        batch_match = progress_bar_pattern.search(text)
        if batch_match:
            print(f"✓ progress_bar_pattern 匹配: {batch_match.group(1)}% ({batch_match.group(2)}/{batch_match.group(3)})")
        
        # 测试进度条检测
        if '█' in text and '%' in text and '|' in text:
            print("✓ 检测到进度条")
            progress_match = yolo_progress_pattern.search(text)
            if progress_match:
                percent = int(progress_match.group(1))
                print(f"✓ 进度百分比: {percent}%")
                
                # 尝试提取批次信息
                if '/' in text:
                    parts = text.split('|')
                    for part in parts:
                        if '/' in part:
                            try:
                                numbers = part.strip().split()
                                for num_part in numbers:
                                    if '/' in num_part:
                                        current, total = num_part.split('/')
                                        print(f"✓ 批次信息: {current}/{total}")
                                        break
                                break
                            except:
                                continue

if __name__ == "__main__":
    test_patterns()
