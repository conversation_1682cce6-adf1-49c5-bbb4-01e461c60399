#!/usr/bin/env python3
"""
测试停止训练功能
"""

import os
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

from core.trainer import YOLOTrainer

def test_stop_training():
    """测试停止训练功能"""
    print("=" * 60)
    print("测试停止训练功能")
    print("=" * 60)
    
    # 创建训练器
    trainer = YOLOTrainer()
    
    # 加载模型
    model_path = "models/yolov8n-seg.pt"
    if not os.path.exists(model_path):
        print(f"错误：模型文件不存在: {model_path}")
        return False
    
    success = trainer.load_custom_model(model_path)
    if not success:
        print("模型加载失败")
        return False
    
    print(f"✓ 模型加载成功: {model_path}")
    
    # 使用内置的coco8-seg数据集（会自动下载）
    data_config = "coco8-seg.yaml"
    
    # 设置训练配置 - 使用较长的训练时间来测试停止功能
    training_config = {
        'epochs': 10,     # 10个轮次，足够长来测试停止
        'batch': 6,       # 用户指定的批次大小
        'workers': 0,     # 用户指定的workers数量
        'imgsz': 640,     # 标准图像尺寸
        'device': 'cpu',  # 使用CPU避免GPU问题
        'cache': False,
        'plots': True,
        'verbose': True,
        'save': True,
        'save_period': 1  # 每个轮次都保存
    }
    
    # 回调函数
    log_messages = []
    progress_updates = []
    metrics_updates = []
    batch_progress_updates = []
    
    def log_callback(message):
        log_messages.append(message)
        print(f"[LOG] {message}")
    
    def progress_callback(progress, message):
        progress_updates.append((progress, message))
        print(f"[PROGRESS] {progress}% - {message}")
    
    def metrics_callback(metrics):
        metrics_updates.append(metrics)
        print(f"[METRICS] 收到指标更新: {len(metrics)} 个指标")
    
    def batch_progress_callback(progress, message):
        batch_progress_updates.append((progress, message))
        print(f"[BATCH] {progress}% - {message}")
    
    # 启动训练线程
    training_thread = None
    
    def start_training():
        nonlocal training_thread
        print("\n开始训练...")
        try:
            # 设置训练配置
            trainer.set_training_config(training_config)

            success = trainer.train(
                data_config,
                progress_callback,
                log_callback,
                metrics_callback,
                batch_progress_callback
            )
            print(f"训练结果: {success}")
        except Exception as e:
            print(f"训练异常: {e}")
    
    # 在单独的线程中启动训练
    training_thread = threading.Thread(target=start_training)
    training_thread.daemon = True
    training_thread.start()
    
    # 等待训练开始
    print("等待训练开始...")
    time.sleep(5)
    
    # 检查训练是否已经开始
    if trainer.is_training:
        print("✓ 训练已开始")
        
        # 等待一段时间让训练进行
        print("让训练运行10秒...")
        time.sleep(10)
        
        # 停止训练
        print("\n🛑 测试停止训练功能...")
        trainer.stop_training()
        
        # 等待训练线程结束
        print("等待训练线程结束...")
        training_thread.join(timeout=10)
        
        if training_thread.is_alive():
            print("⚠️ 训练线程仍在运行")
        else:
            print("✓ 训练线程已结束")
    else:
        print("❌ 训练未能启动")
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("测试结果统计:")
    print(f"日志消息数量: {len(log_messages)}")
    print(f"进度更新数量: {len(progress_updates)}")
    print(f"指标更新数量: {len(metrics_updates)}")
    print(f"批次进度更新数量: {len(batch_progress_updates)}")
    print(f"训练器状态: {'运行中' if trainer.is_training else '已停止'}")
    
    # 检查exp文件夹
    runs_dir = Path("runs/train")
    if runs_dir.exists():
        exp_dirs = list(runs_dir.glob("exp*"))
        if exp_dirs:
            latest_exp = max(exp_dirs, key=lambda x: x.stat().st_mtime)
            csv_file = latest_exp / "results.csv"
            if csv_file.exists():
                print(f"✓ CSV文件已生成: {csv_file}")
                # 读取CSV文件内容
                try:
                    import pandas as pd
                    df = pd.read_csv(csv_file)
                    print(f"  CSV文件包含 {len(df)} 行数据")
                except Exception as e:
                    print(f"  读取CSV文件失败: {e}")
            else:
                print(f"❌ CSV文件未生成: {csv_file}")
        else:
            print("❌ 未找到exp文件夹")
    else:
        print("❌ runs/train目录不存在")
    
    print("=" * 60)
    return True

if __name__ == "__main__":
    test_stop_training()
