#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图片的脚本
"""

import os
import numpy as np
from PIL import Image, ImageDraw
from pathlib import Path

def create_test_image(width=320, height=320, color=(128, 128, 128)):
    """创建一个简单的测试图片"""
    # 创建图片
    img = Image.new('RGB', (width, height), color)
    draw = ImageDraw.Draw(img)
    
    # 画一些简单的形状
    # 画一个矩形
    draw.rectangle([50, 50, 150, 150], outline=(255, 0, 0), width=3)
    
    # 画一个圆
    draw.ellipse([200, 100, 280, 180], outline=(0, 255, 0), width=3)
    
    # 画一些线条
    draw.line([10, 10, 100, 100], fill=(0, 0, 255), width=2)
    draw.line([10, 100, 100, 10], fill=(255, 255, 0), width=2)
    
    return img

def create_sample_images():
    """创建示例图片"""
    print("创建测试图片...")
    
    # 训练图片
    train_images_dir = Path("sample_dataset/train/images")
    train_images_dir.mkdir(parents=True, exist_ok=True)
    
    # 验证图片
    val_images_dir = Path("sample_dataset/val/images")
    val_images_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建训练图片
    for i in range(1, 4):
        img = create_test_image(
            color=(100 + i * 20, 100 + i * 15, 100 + i * 10)
        )
        img_path = train_images_dir / f"train_img{i}.jpg"
        img.save(img_path)
        print(f"创建训练图片: {img_path}")
    
    # 创建验证图片
    for i in range(1, 3):
        img = create_test_image(
            color=(150 + i * 10, 150 + i * 20, 150 + i * 15)
        )
        img_path = val_images_dir / f"val_img{i}.jpg"
        img.save(img_path)
        print(f"创建验证图片: {img_path}")
    
    print("✅ 测试图片创建完成!")

if __name__ == "__main__":
    create_sample_images()
