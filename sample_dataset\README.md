# 示例数据集

这是一个YOLO训练的示例数据集结构。

## 目录结构
```
sample_dataset/
├── train/
│   ├── images/          # 训练图片目录
│   └── labels/          # 训练标签目录
├── val/
│   ├── images/          # 验证图片目录
│   └── labels/          # 验证标签目录
└── data.yaml           # 数据集配置文件
```

## 使用说明

1. 将您的训练图片放入 `train/images/` 目录
2. 将您的验证图片放入 `val/images/` 目录
3. 确保每张图片都有对应的标签文件
4. 在训练界面中选择 `sample_dataset` 目录
5. 点击"验证数据集"按钮

## 标签格式

每个标签文件包含该图片中所有目标的标注：
```
class_id center_x center_y width height
```

- class_id: 类别ID（从0开始）
- center_x, center_y: 目标中心点坐标（归一化，0-1）
- width, height: 目标宽度和高度（归一化，0-1）

## 类别说明

本示例数据集包含3个类别：
- 0: person（人）
- 1: car（汽车）
- 2: bike（自行车）

您可以根据需要修改 `data.yaml` 文件中的类别定义。
