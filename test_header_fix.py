#!/usr/bin/env python3
"""
测试表头修复
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_header_fix():
    """测试表头修复"""
    print("=" * 60)
    print("测试表头修复")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练组件
        widget = TrainingWidget()
        
        # 测试实际的YOLO输出序列（包含\r的情况）
        test_messages = [
            "Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU",
            "Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs",
            "      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size",
            "      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:   0%|          | 0/96 [00:00<?, ?it/s]",
            "\r      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:  25%|##5       | 25/96 [00:10<00:30,  2.32it/s]",
            "\r      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:  50%|#####     | 50/96 [00:20<00:20,  2.32it/s]",
            "\r      1/100     0.975G      1.497      3.261      4.773      1.351         45        640:  75%|#######5  | 75/96 [00:30<00:10,  2.32it/s]",
            "\r      1/100     0.975G      1.497      3.261      4.773      1.351         45        640: 100%|##########| 96/96 [00:40<00:00,  2.32it/s]",
        ]
        
        print("测试消息的进度行检测结果:")
        for i, message in enumerate(test_messages):
            # 清理\r字符进行检测
            clean_message = message.replace('\r', '').strip()
            is_progress = widget._is_progress_line(clean_message)
            print(f"{i+1}. {'[PROGRESS]' if is_progress else '[NORMAL]  '} {clean_message[:80]}...")
        
        print("\n开始模拟实际添加过程:")
        
        for i, message in enumerate(test_messages):
            print(f"\n步骤 {i+1}: 添加消息")
            clean_message = message.replace('\r', '').strip()
            print(f"消息: {clean_message[:80]}...")
            
            # 模拟终端捕获器的处理
            if '\r' in message:
                parts = message.split('\r')
                for part in parts:
                    if part.strip():
                        widget.add_log(part.strip())
            else:
                widget.add_log(message)
            
            # 检查当前日志内容
            current_text = widget.log_text.toPlainText()
            lines = current_text.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            print(f"当前日志行数: {len(non_empty_lines)}")
            print("当前日志内容:")
            for j, line in enumerate(non_empty_lines):
                print(f"  {j+1}. {line}")
        
        # 最终检查
        print("\n" + "=" * 60)
        print("最终结果分析")
        print("=" * 60)
        
        final_text = widget.log_text.toPlainText()
        final_lines = final_text.split('\n')
        final_non_empty = [line for line in final_lines if line.strip()]
        
        print(f"最终日志行数: {len(final_non_empty)}")
        print("最终日志内容:")
        for i, line in enumerate(final_non_empty):
            print(f"  {i+1}. {line}")
        
        # 检查关键内容
        has_basic_info = any('Ultralytics YOLOv8' in line for line in final_non_empty)
        has_model_summary = any('Model summary' in line for line in final_non_empty)
        has_train_header = any('Epoch    GPU_mem   box_loss' in line for line in final_non_empty)
        has_final_progress = any('100%|##########|' in line for line in final_non_empty)
        
        print(f"\n关键检查:")
        print(f"✓ 基本信息: {'是' if has_basic_info else '否'}")
        print(f"✓ 模型摘要: {'是' if has_model_summary else '否'}")
        print(f"✓ 训练表头: {'是' if has_train_header else '否'}")
        print(f"✓ 最终进度: {'是' if has_final_progress else '否'}")
        
        # 检查行数是否合理
        expected_lines = 4  # 基本信息 + 模型摘要 + 训练表头 + 最终进度
        reasonable_line_count = len(final_non_empty) <= expected_lines + 1
        
        print(f"✓ 行数合理: {'是' if reasonable_line_count else '否'} ({len(final_non_empty)}/{expected_lines})")
        
        success = has_basic_info and has_model_summary and has_train_header and has_final_progress and reasonable_line_count
        
        print(f"\n测试结果: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print("🎉 表头修复成功！")
            print("现在训练表头将正确显示，不会被进度行覆盖。")
        else:
            print("⚠️ 表头修复需要进一步调试。")
            
            if not has_train_header:
                print("问题：训练表头仍然丢失")
            if not reasonable_line_count:
                print("问题：日志行数过多，可能存在重复")
        
        return success
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试表头修复...")
    
    success = test_header_fix()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 表头修复测试通过！")
        print("现在可以重新启动训练，应该会看到：")
        print("1. 完整的基本信息")
        print("2. 保留的训练表头")
        print("3. 动态更新的训练进度")
        print("4. 合理的日志行数")
        print("\n您的问题已经解决！")
    else:
        print("❌ 测试失败，需要进一步调试。")
    
    print("=" * 60)
