#!/usr/bin/env python3
"""
简单的终端输出同步测试
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_terminal_capture():
    """测试终端捕获功能"""
    print("=" * 60)
    print("测试终端输出捕获功能")
    print("=" * 60)
    
    # 捕获的日志
    captured_logs = []
    
    def safe_log_capture(message):
        """安全的日志捕获，避免递归"""
        captured_logs.append(message)
        # 直接写入到原始stdout，避免递归
        sys.__stdout__.write(f"[CAPTURED] {message}\n")
        sys.__stdout__.flush()
    
    # 创建终端捕获器
    from ui.widgets.training_widget import TerminalCapture
    terminal_capture = TerminalCapture(safe_log_capture)
    
    print("开始测试终端输出捕获...")
    
    try:
        # 开始捕获
        terminal_capture.start_capture()
        
        # 测试基本输出
        print("这是第一条测试消息")
        print("这是第二条测试消息")
        
        # 测试多行输出
        print("多行输出测试:")
        print("  - 第一行")
        print("  - 第二行")
        print("  - 第三行")
        
        # 模拟进度条输出
        print("模拟进度条输出:")
        for i in range(3):
            print(f"\r进度: {i*33}%|{'█'*i}{'▌'*(3-i)}| {i}/3", end='', flush=True)
            time.sleep(0.5)
        print()  # 换行
        
        # 测试带颜色的输出（ANSI转义序列）
        print("\033[32m这是绿色文本\033[0m")
        print("\033[31m这是红色文本\033[0m")
        
        # 测试YOLO风格的输出
        print("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
        print("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
        print("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        print("        1/2      0.00G      1.234      0.567      0.890      1.234         32        640")
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试异常: {e}")
        
    finally:
        # 停止捕获
        terminal_capture.stop_capture()
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("终端输出捕获测试结果:")
    print(f"捕获的日志条数: {len(captured_logs)}")
    
    # 显示捕获的日志
    print("\n捕获的日志内容:")
    for i, log in enumerate(captured_logs):
        print(f"  {i+1:2d}. {log}")
    
    # 检查是否捕获到特定内容
    test_keywords = ['测试消息', '进度:', 'YOLOv8', 'Epoch', 'GPU_mem']
    found_keywords = []
    
    for keyword in test_keywords:
        for log in captured_logs:
            if keyword in log:
                found_keywords.append(keyword)
                break
    
    print(f"\n检测到的关键词: {found_keywords}")
    
    # 检查进度相关输出
    progress_logs = [log for log in captured_logs if '[PROGRESS]' in log or '进度:' in log]
    print(f"进度相关日志条数: {len(progress_logs)}")
    
    if progress_logs:
        print("进度日志示例:")
        for log in progress_logs:
            print(f"  {log}")
    
    print("=" * 60)
    print("终端输出捕获功能测试完成")
    print("功能验证:")
    print(f"✓ 基本输出捕获: {'成功' if len(captured_logs) > 0 else '失败'}")
    print(f"✓ 关键词检测: {'成功' if len(found_keywords) > 0 else '失败'}")
    print(f"✓ 进度输出捕获: {'成功' if len(progress_logs) > 0 else '失败'}")
    print(f"✓ 无递归错误: {'成功' if len(captured_logs) < 100 else '失败'}")  # 如果有递归，日志会非常多
    print("=" * 60)
    
    return len(captured_logs) > 0 and len(found_keywords) > 0 and len(captured_logs) < 100

if __name__ == "__main__":
    success = test_terminal_capture()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    
    if success:
        print("\n✅ 终端输出同步功能工作正常！")
        print("现在可以在GUI中使用这个功能来实时同步训练日志。")
    else:
        print("\n❌ 终端输出同步功能存在问题，需要进一步调试。")
