#!/usr/bin/env python3
"""
基于CSV文件的训练数据监控器
从 runs/train/exp.../results.csv 文件实时读取训练数据
"""

import os
import csv
import time
import glob
from pathlib import Path
from typing import Dict, List, Optional, Callable
from PySide6.QtCore import QObject, Signal, QTimer, QFileSystemWatcher


class CSVTrainingMonitor(QObject):
    """CSV训练数据监控器"""
    
    # 信号定义
    data_updated = Signal(dict)  # 新的训练数据
    epoch_completed = Signal(int, dict)  # 轮次完成
    training_started = Signal(str)  # 训练开始，传递CSV文件路径
    training_finished = Signal()  # 训练结束
    
    def __init__(self):
        super().__init__()
        self.csv_file_path = None
        self.last_epoch = 0
        self.monitoring = False
        
        # 文件系统监控器
        self.file_watcher = QFileSystemWatcher()
        self.file_watcher.fileChanged.connect(self.on_file_changed)
        
        # 定时器用于检查新的训练任务
        self.check_timer = QTimer()
        self.check_timer.timeout.connect(self.check_for_new_training)
        self.check_timer.start(2000)  # 每2秒检查一次
        
        # 数据缓存
        self.training_data = []
        self.column_names = []
    
    def start_monitoring(self, csv_path: Optional[str] = None):
        """开始监控训练数据"""
        if csv_path:
            self.csv_file_path = csv_path
        else:
            # 自动查找最新的训练CSV文件
            self.csv_file_path = self.find_latest_training_csv()
        
        if self.csv_file_path and os.path.exists(self.csv_file_path):
            print(f"开始监控CSV文件: {self.csv_file_path}")
            
            # 添加文件监控
            if self.csv_file_path not in self.file_watcher.files():
                self.file_watcher.addPath(self.csv_file_path)
            
            # 读取初始数据
            self.load_csv_data()
            self.monitoring = True
            
            # 发送训练开始信号
            self.training_started.emit(self.csv_file_path)
            
            return True
        else:
            print("未找到训练CSV文件")
            return False
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.csv_file_path and self.csv_file_path in self.file_watcher.files():
            self.file_watcher.removePath(self.csv_file_path)
        self.csv_file_path = None
        self.last_epoch = 0
        self.training_data.clear()
        print("停止监控训练数据")
    
    def find_latest_training_csv(self) -> Optional[str]:
        """查找最新的训练CSV文件"""
        try:
            # 查找runs/train目录下的所有results.csv文件
            pattern = "runs/train/*/results.csv"
            csv_files = glob.glob(pattern)
            
            if not csv_files:
                return None
            
            # 按修改时间排序，获取最新的
            latest_file = max(csv_files, key=os.path.getmtime)
            
            # 检查文件是否在最近5分钟内修改过（说明是活跃的训练）
            if time.time() - os.path.getmtime(latest_file) < 300:  # 5分钟
                return latest_file
            
            return None
            
        except Exception as e:
            print(f"查找CSV文件时出错: {e}")
            return None
    
    def check_for_new_training(self):
        """检查是否有新的训练任务"""
        if not self.monitoring:
            new_csv = self.find_latest_training_csv()
            if new_csv and new_csv != self.csv_file_path:
                print(f"发现新的训练任务: {new_csv}")
                self.start_monitoring(new_csv)
    
    def on_file_changed(self, file_path: str):
        """文件变化时的回调"""
        if file_path == self.csv_file_path and self.monitoring:
            print(f"检测到CSV文件更新: {file_path}")
            self.load_csv_data()
    
    def load_csv_data(self):
        """加载CSV数据"""
        try:
            if not os.path.exists(self.csv_file_path):
                return
            
            with open(self.csv_file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                rows = list(reader)
            
            if len(rows) < 2:  # 至少需要标题行和一行数据
                return
            
            # 解析列名
            self.column_names = rows[0]
            
            # 解析数据行
            new_data = []
            for row in rows[1:]:
                if len(row) == len(self.column_names) and row[0].strip():  # 确保行完整且有epoch数据
                    try:
                        data_dict = {}
                        for i, value in enumerate(row):
                            col_name = self.column_names[i]
                            # 尝试转换为数字
                            try:
                                if '.' in value:
                                    data_dict[col_name] = float(value)
                                else:
                                    data_dict[col_name] = int(value)
                            except ValueError:
                                data_dict[col_name] = value
                        new_data.append(data_dict)
                    except Exception as e:
                        print(f"解析数据行时出错: {e}")
                        continue
            
            # 检查是否有新数据
            if len(new_data) > len(self.training_data):
                # 有新的轮次数据
                self.training_data = new_data
                latest_data = new_data[-1]
                current_epoch = latest_data.get('epoch', 0)
                
                if current_epoch > self.last_epoch:
                    print(f"新轮次数据: Epoch {current_epoch}")
                    self.last_epoch = current_epoch
                    
                    # 发送信号
                    self.data_updated.emit(latest_data)
                    self.epoch_completed.emit(current_epoch, latest_data)
                
        except Exception as e:
            print(f"加载CSV数据时出错: {e}")
    
    def get_training_history(self) -> List[Dict]:
        """获取完整的训练历史"""
        return self.training_data.copy()
    
    def get_latest_data(self) -> Optional[Dict]:
        """获取最新的训练数据"""
        if self.training_data:
            return self.training_data[-1]
        return None
    
    def get_metrics_for_plotting(self) -> Dict[str, List]:
        """获取用于绘图的指标数据"""
        if not self.training_data:
            return {}
        
        metrics = {}
        for key in self.column_names:
            metrics[key] = []
        
        for data in self.training_data:
            for key in self.column_names:
                metrics[key].append(data.get(key, 0))
        
        return metrics


class SimpleTerminalCapture(QObject):
    """简化的终端输出捕获器 - 只负责显示日志"""
    
    log_output = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.original_stdout = None
        self.capturing = False
    
    def start_capture(self):
        """开始捕获终端输出"""
        if not self.capturing:
            import sys
            self.original_stdout = sys.stdout
            sys.stdout = self
            self.capturing = True
    
    def stop_capture(self):
        """停止捕获终端输出"""
        if self.capturing:
            import sys
            sys.stdout = self.original_stdout
            self.capturing = False
    
    def write(self, text):
        """重写write方法"""
        # 写入原始输出
        if self.original_stdout:
            self.original_stdout.write(text)
            self.original_stdout.flush()
        
        # 发送日志信号
        if text.strip() and self.capturing:
            self.log_output.emit(text.strip())
        
        return len(text)
    
    def flush(self):
        """刷新输出"""
        if self.original_stdout:
            self.original_stdout.flush()


class CombinedTrainingMonitor(QObject):
    """组合的训练监控器 - 结合CSV数据和终端输出"""
    
    # 信号定义
    log_output = Signal(str)  # 终端日志输出
    data_updated = Signal(dict)  # 训练数据更新
    epoch_completed = Signal(int, dict)  # 轮次完成
    training_started = Signal()  # 训练开始
    training_finished = Signal()  # 训练结束
    
    def __init__(self):
        super().__init__()
        
        # CSV数据监控器
        self.csv_monitor = CSVTrainingMonitor()
        self.csv_monitor.data_updated.connect(self.data_updated.emit)
        self.csv_monitor.epoch_completed.connect(self.epoch_completed.emit)
        self.csv_monitor.training_started.connect(lambda path: self.training_started.emit())
        self.csv_monitor.training_finished.connect(self.training_finished.emit)
        
        # 终端输出捕获器
        self.terminal_capture = SimpleTerminalCapture()
        self.terminal_capture.log_output.connect(self.log_output.emit)
    
    def start_monitoring(self):
        """开始监控"""
        print("开始组合监控...")
        self.csv_monitor.start_monitoring()
        self.terminal_capture.start_capture()
    
    def stop_monitoring(self):
        """停止监控"""
        print("停止组合监控...")
        self.csv_monitor.stop_monitoring()
        self.terminal_capture.stop_capture()
    
    def get_training_history(self) -> List[Dict]:
        """获取训练历史"""
        return self.csv_monitor.get_training_history()
    
    def get_metrics_for_plotting(self) -> Dict[str, List]:
        """获取绘图数据"""
        return self.csv_monitor.get_metrics_for_plotting()


def test_csv_monitor():
    """测试CSV监控器"""
    import sys
    from PySide6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    monitor = CombinedTrainingMonitor()
    
    def on_log(message):
        print(f"[LOG] {message}")
    
    def on_data_updated(data):
        print(f"[DATA] 数据更新: Epoch {data.get('epoch', '?')}")
        print(f"       训练损失: {data.get('train/box_loss', '?'):.4f}")
        print(f"       验证损失: {data.get('val/box_loss', '?'):.4f}")
        print(f"       mAP50: {data.get('metrics/mAP50(B)', '?'):.4f}")
    
    def on_epoch_completed(epoch, data):
        print(f"[EPOCH] 轮次 {epoch} 完成")
    
    monitor.log_output.connect(on_log)
    monitor.data_updated.connect(on_data_updated)
    monitor.epoch_completed.connect(on_epoch_completed)
    
    monitor.start_monitoring()
    
    print("CSV监控器测试启动，等待训练数据...")
    print("请在另一个终端启动YOLO训练来测试")
    
    return app.exec()


if __name__ == "__main__":
    test_csv_monitor()
