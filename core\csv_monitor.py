#!/usr/bin/env python3
"""
基于CSV文件的训练数据监控器
从 runs/train/exp.../results.csv 文件实时读取训练数据
"""

import os
import csv
import time
import glob
from pathlib import Path
from typing import Dict, List, Optional, Callable
from PySide6.QtCore import QObject, Signal, QTimer, QFileSystemWatcher


class CSVTrainingMonitor(QObject):
    """CSV训练数据监控器"""
    
    # 信号定义
    data_updated = Signal(dict)  # 新的训练数据
    epoch_completed = Signal(int, dict)  # 轮次完成
    training_started = Signal(str)  # 训练开始，传递CSV文件路径
    training_finished = Signal()  # 训练结束
    
    def __init__(self):
        super().__init__()
        self.csv_file_path = None
        self.last_epoch = 0
        self.monitoring = False
        
        # 文件系统监控器
        self.file_watcher = QFileSystemWatcher()
        self.file_watcher.fileChanged.connect(self.on_file_changed)
        
        # 定时器用于检查新的训练任务
        self.check_timer = QTimer()
        self.check_timer.timeout.connect(self.check_for_new_training)
        self.check_timer.start(2000)  # 每2秒检查一次
        
        # 数据缓存
        self.training_data = []
        self.column_names = []
    
    def start_monitoring(self, csv_path: Optional[str] = None):
        """开始监控训练数据"""
        if csv_path:
            self.csv_file_path = csv_path
        else:
            # 自动查找最新的训练CSV文件
            self.csv_file_path = self.find_latest_training_csv()
        
        if self.csv_file_path and os.path.exists(self.csv_file_path):
            print(f"开始监控CSV文件: {self.csv_file_path}")
            
            # 添加文件监控
            if self.csv_file_path not in self.file_watcher.files():
                self.file_watcher.addPath(self.csv_file_path)
            
            # 读取初始数据
            self.load_csv_data()
            self.monitoring = True
            
            # 发送训练开始信号
            self.training_started.emit(self.csv_file_path)
            
            return True
        else:
            print("未找到训练CSV文件")
            return False
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.csv_file_path and self.csv_file_path in self.file_watcher.files():
            self.file_watcher.removePath(self.csv_file_path)
        self.csv_file_path = None
        self.last_epoch = 0
        self.training_data.clear()
        print("停止监控训练数据")
    
    def find_latest_training_csv(self) -> Optional[str]:
        """查找最新的训练CSV文件"""
        try:
            # 查找runs/train目录下的所有results.csv文件
            pattern = "runs/train/*/results.csv"
            csv_files = glob.glob(pattern)
            
            if not csv_files:
                return None
            
            # 按修改时间排序，获取最新的
            latest_file = max(csv_files, key=os.path.getmtime)
            
            # 检查文件是否在最近5分钟内修改过（说明是活跃的训练）
            if time.time() - os.path.getmtime(latest_file) < 300:  # 5分钟
                return latest_file
            
            return None
            
        except Exception as e:
            print(f"查找CSV文件时出错: {e}")
            return None
    
    def check_for_new_training(self):
        """检查是否有新的训练任务"""
        if not self.monitoring:
            new_csv = self.find_latest_training_csv()
            if new_csv and new_csv != self.csv_file_path:
                print(f"发现新的训练任务: {new_csv}")
                self.start_monitoring(new_csv)
    
    def on_file_changed(self, file_path: str):
        """文件变化时的回调"""
        if file_path == self.csv_file_path and self.monitoring:
            print(f"检测到CSV文件更新: {file_path}")
            self.load_csv_data()
    
    def load_csv_data(self):
        """加载CSV数据"""
        try:
            if not os.path.exists(self.csv_file_path):
                return
            
            with open(self.csv_file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                rows = list(reader)
            
            if len(rows) < 2:  # 至少需要标题行和一行数据
                return
            
            # 解析列名
            self.column_names = rows[0]
            
            # 解析数据行
            new_data = []
            for row in rows[1:]:
                if len(row) == len(self.column_names) and row[0].strip():  # 确保行完整且有epoch数据
                    try:
                        data_dict = {}
                        for i, value in enumerate(row):
                            col_name = self.column_names[i]
                            # 尝试转换为数字
                            try:
                                if '.' in value:
                                    data_dict[col_name] = float(value)
                                else:
                                    data_dict[col_name] = int(value)
                            except ValueError:
                                data_dict[col_name] = value
                        new_data.append(data_dict)
                    except Exception as e:
                        print(f"解析数据行时出错: {e}")
                        continue
            
            # 检查是否有新数据
            if len(new_data) > len(self.training_data):
                # 有新的轮次数据
                self.training_data = new_data
                latest_data = new_data[-1]
                current_epoch = latest_data.get('epoch', 0)
                
                if current_epoch > self.last_epoch:
                    print(f"新轮次数据: Epoch {current_epoch}")
                    self.last_epoch = current_epoch
                    
                    # 发送信号
                    self.data_updated.emit(latest_data)
                    self.epoch_completed.emit(current_epoch, latest_data)
                
        except Exception as e:
            print(f"加载CSV数据时出错: {e}")
    
    def get_training_history(self) -> List[Dict]:
        """获取完整的训练历史"""
        return self.training_data.copy()
    
    def get_latest_data(self) -> Optional[Dict]:
        """获取最新的训练数据"""
        if self.training_data:
            return self.training_data[-1]
        return None
    
    def get_metrics_for_plotting(self) -> Dict[str, List]:
        """获取用于绘图的指标数据"""
        if not self.training_data:
            return {}
        
        metrics = {}
        for key in self.column_names:
            metrics[key] = []
        
        for data in self.training_data:
            for key in self.column_names:
                metrics[key].append(data.get(key, 0))
        
        return metrics


class SimpleFileMonitor(QObject):
    """简单的文件监控器 - 监控训练目录和CSV文件"""

    log_output = Signal(str)
    progress_update = Signal(int, int, str)  # current_epoch, total_epochs, message
    batch_progress_update = Signal(int, str)  # batch_progress_percent, message
    exp_folder_detected = Signal(str)  # exp文件夹路径

    def __init__(self):
        super().__init__()
        self.monitoring = False
        self.exp_folder = None
        self.csv_path = None
        self.last_csv_size = 0
        self.current_epoch = 0
        self.total_epochs = 0

        # 创建定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_training_progress)

        # 文件系统监控器
        self.file_watcher = QFileSystemWatcher()
        self.file_watcher.directoryChanged.connect(self.on_directory_changed)

    def start_monitoring(self, total_epochs=100):
        """开始监控训练"""
        if not self.monitoring:
            self.monitoring = True
            self.total_epochs = total_epochs
            self.current_epoch = 0

            # 监控runs/train目录
            runs_train_dir = "runs/train"
            if os.path.exists(runs_train_dir):
                self.file_watcher.addPath(runs_train_dir)

            # 启动定时器，每2秒检查一次
            self.timer.start(2000)
            self.log_output.emit("开始监控训练进度...")

    def stop_monitoring(self):
        """停止监控"""
        if self.monitoring:
            self.monitoring = False
            self.timer.stop()

            # 清理文件监控器
            paths = self.file_watcher.directories() + self.file_watcher.files()
            if paths:
                self.file_watcher.removePaths(paths)

            self.log_output.emit("停止监控训练进度...")

    def on_directory_changed(self, path):
        """当目录发生变化时调用"""
        if not self.monitoring:
            return

        # 检查是否有新的exp文件夹
        if "runs/train" in path:
            self.detect_latest_exp_folder()

    def detect_latest_exp_folder(self):
        """检测最新的exp文件夹"""
        runs_train_dir = Path("runs/train")
        if not runs_train_dir.exists():
            return

        # 查找所有exp文件夹
        exp_dirs = list(runs_train_dir.glob("exp*"))
        if not exp_dirs:
            return

        # 找到最新的exp文件夹
        latest_exp = max(exp_dirs, key=lambda x: x.stat().st_mtime)
        exp_folder_path = str(latest_exp)

        # 如果是新的exp文件夹
        if self.exp_folder != exp_folder_path:
            self.exp_folder = exp_folder_path
            self.csv_path = latest_exp / "results.csv"
            self.last_csv_size = 0

            self.log_output.emit(f"检测到新的训练文件夹: {exp_folder_path}")
            self.exp_folder_detected.emit(exp_folder_path)

            # 监控CSV文件
            if self.csv_path.exists():
                csv_path_str = str(self.csv_path)
                if csv_path_str not in self.file_watcher.files():
                    self.file_watcher.addPath(csv_path_str)

    def check_training_progress(self):
        """检查训练进度"""
        if not self.monitoring:
            return

        # 如果还没有检测到exp文件夹，尝试检测
        if not self.exp_folder:
            self.detect_latest_exp_folder()
            return

        # 检查CSV文件
        if self.csv_path and self.csv_path.exists():
            self.check_csv_updates()

    def check_csv_updates(self):
        """检查CSV文件更新"""
        try:
            current_size = self.csv_path.stat().st_size
            if current_size > self.last_csv_size:
                self.last_csv_size = current_size

                # 读取CSV文件的最新数据
                import pandas as pd
                df = pd.read_csv(self.csv_path)

                if not df.empty:
                    latest_row = df.iloc[-1]
                    epoch = len(df)  # CSV文件的行数就是轮次数

                    # 更新当前轮次
                    if epoch > self.current_epoch:
                        self.current_epoch = epoch
                        message = f"完成轮次 {epoch}/{self.total_epochs}"
                        self.progress_update.emit(epoch, self.total_epochs, message)

                        # 发送训练指标
                        metrics = latest_row.to_dict()
                        self.log_output.emit(f"轮次 {epoch} 指标: {metrics}")

                        # 模拟批次进度（轮次完成时为100%）
                        self.batch_progress_update.emit(100, f"轮次 {epoch} 完成")

        except Exception as e:
            self.log_output.emit(f"读取CSV文件错误: {e}")

    def flush(self):
        """兼容性方法"""
        pass


class CombinedTrainingMonitor(QObject):
    """简化的训练监控器 - 基于文件监控"""

    # 信号定义
    log_output = Signal(str)  # 日志输出
    data_updated = Signal(dict)  # 训练数据更新
    epoch_completed = Signal(int, dict)  # 轮次完成
    progress_update = Signal(int, int, str)  # 轮次进度更新
    batch_progress_update = Signal(int, str)  # 批次进度更新
    training_started = Signal()  # 训练开始
    training_finished = Signal()  # 训练结束

    def __init__(self):
        super().__init__()

        # 使用简单的文件监控器
        self.file_monitor = SimpleFileMonitor()
        self.file_monitor.log_output.connect(self.log_output.emit)
        self.file_monitor.progress_update.connect(self.progress_update.emit)
        self.file_monitor.batch_progress_update.connect(self.batch_progress_update.emit)
        self.file_monitor.exp_folder_detected.connect(self.on_exp_folder_detected)

        # CSV数据监控器（作为备用）
        self.csv_monitor = CSVTrainingMonitor()
        self.csv_monitor.data_updated.connect(self.data_updated.emit)
        self.csv_monitor.epoch_completed.connect(self.epoch_completed.emit)
        self.csv_monitor.training_started.connect(lambda path: self.training_started.emit())
        self.csv_monitor.training_finished.connect(self.training_finished.emit)

    def on_exp_folder_detected(self, exp_folder_path):
        """当检测到exp文件夹时，开始监控对应的CSV文件"""
        csv_path = f"{exp_folder_path}/results.csv"
        self.log_output.emit(f"检测到训练文件夹，开始监控: {csv_path}")

        # 启动CSV监控器作为补充
        self.csv_monitor.csv_path = csv_path
        self.csv_monitor.start_monitoring()

    def start_monitoring(self, total_epochs=100):
        """开始监控"""
        self.log_output.emit("开始训练监控...")
        self.training_started.emit()

        # 启动文件监控器
        self.file_monitor.start_monitoring(total_epochs)

    def stop_monitoring(self):
        """停止监控"""
        self.log_output.emit("停止训练监控...")

        # 停止所有监控器
        self.file_monitor.stop_monitoring()
        self.csv_monitor.stop_monitoring()

        self.training_finished.emit()
    
    def get_training_history(self) -> List[Dict]:
        """获取训练历史"""
        return self.csv_monitor.get_training_history()
    
    def get_metrics_for_plotting(self) -> Dict[str, List]:
        """获取绘图数据"""
        return self.csv_monitor.get_metrics_for_plotting()


def test_csv_monitor():
    """测试CSV监控器"""
    import sys
    from PySide6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    monitor = CombinedTrainingMonitor()
    
    def on_log(message):
        print(f"[LOG] {message}")
    
    def on_data_updated(data):
        print(f"[DATA] 数据更新: Epoch {data.get('epoch', '?')}")
        print(f"       训练损失: {data.get('train/box_loss', '?'):.4f}")
        print(f"       验证损失: {data.get('val/box_loss', '?'):.4f}")
        print(f"       mAP50: {data.get('metrics/mAP50(B)', '?'):.4f}")
    
    def on_epoch_completed(epoch, data):
        print(f"[EPOCH] 轮次 {epoch} 完成")
    
    monitor.log_output.connect(on_log)
    monitor.data_updated.connect(on_data_updated)
    monitor.epoch_completed.connect(on_epoch_completed)
    
    monitor.start_monitoring()
    
    print("CSV监控器测试启动，等待训练数据...")
    print("请在另一个终端启动YOLO训练来测试")
    
    return app.exec()


if __name__ == "__main__":
    test_csv_monitor()
