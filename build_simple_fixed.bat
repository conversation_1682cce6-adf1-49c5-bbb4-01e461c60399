@echo off
chcp 65001 > nul
echo YOLO目标检测系统 - 简化打包脚本
echo =====================================
echo.

REM 设置环境变量
set KMP_DUPLICATE_LIB_OK=TRUE

echo 开始打包，这可能需要10-20分钟...
echo.

pyinstaller ^
--onefile ^
--windowed ^
--name="YOLO目标检测系统" ^
--hidden-import=unittest ^
--hidden-import=unittest.mock ^
--hidden-import=collections ^
--hidden-import=collections.abc ^
--hidden-import=importlib ^
--hidden-import=importlib.util ^
--hidden-import=importlib.metadata ^
--hidden-import=typing ^
--hidden-import=typing_extensions ^
--hidden-import=PySide6.QtCore ^
--hidden-import=PySide6.QtGui ^
--hidden-import=PySide6.QtWidgets ^
--hidden-import=PySide6.QtOpenGL ^
--hidden-import=PySide6.QtOpenGLWidgets ^
--hidden-import=torch ^
--hidden-import=torch.nn ^
--hidden-import=torch.nn.functional ^
--hidden-import=torch.fx ^
--hidden-import=torch.fx.passes ^
--hidden-import=torch.fx.passes.shape_prop ^
--hidden-import=torch._dispatch ^
--hidden-import=torch._dispatch.python ^
--hidden-import=torch.export ^
--hidden-import=torchvision ^
--hidden-import=torchvision.transforms ^
--hidden-import=ultralytics ^
--hidden-import=cv2 ^
--hidden-import=numpy ^
--hidden-import=PIL ^
--hidden-import=PIL.Image ^
--hidden-import=onnx ^
--hidden-import=onnxruntime ^
--hidden-import=openvino ^
--hidden-import=ncnn ^
--hidden-import=core ^
--hidden-import=core.detector ^
--hidden-import=core.model_converter ^
--hidden-import=core.utils ^
--hidden-import=ui ^
--hidden-import=ui.main_window ^
--hidden-import=ui.widgets ^
--clean ^
--noconfirm ^
main.py

if %errorlevel% equ 0 (
    echo.
    echo 打包成功！
    echo 可执行文件位置: dist\YOLO目标检测系统.exe
    echo.
    
    REM 创建启动脚本
    echo @echo off > "dist\启动程序.bat"
    echo chcp 65001 ^> nul >> "dist\启动程序.bat"
    echo echo 启动YOLO目标检测系统... >> "dist\启动程序.bat"
    echo echo. >> "dist\启动程序.bat"
    echo "YOLO目标检测系统.exe" >> "dist\启动程序.bat"
    echo if errorlevel 1 ^( >> "dist\启动程序.bat"
    echo     echo. >> "dist\启动程序.bat"
    echo     echo 程序运行出错，请检查系统要求 >> "dist\启动程序.bat"
    echo     pause >> "dist\启动程序.bat"
    echo ^) >> "dist\启动程序.bat"
    
    echo 启动脚本已创建: dist\启动程序.bat
) else (
    echo.
    echo 打包失败！
)

echo.
pause
