#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO目标检测系统打包脚本
使用PyInstaller将项目打包成Windows可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

# 设置环境变量解决OpenMP库冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def print_step(step_num, description):
    """打印步骤信息"""
    print(f"\n{'='*60}")
    print(f"步骤 {step_num}: {description}")
    print(f"{'='*60}")

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"\n执行命令: {command}")
    if description:
        print(f"描述: {description}")

    try:
        # 使用gbk编码处理中文输出，避免编码错误
        result = subprocess.run(command, shell=True, check=True,
                              capture_output=True, text=True,
                              encoding='gbk', errors='ignore')
        if result.stdout:
            print("输出:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        return False

def clean_build_dirs():
    """清理构建目录"""
    print_step(1, "清理构建目录")

    dirs_to_clean = ['build', 'dist', '__pycache__']

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"删除目录: {dir_name}")
            shutil.rmtree(dir_name)
        else:
            print(f"目录不存在: {dir_name}")

    print("清理完成")
    return True

def check_dependencies():
    """检查依赖"""
    print_step(2, "检查依赖")
    
    required_packages = [
        'pyinstaller',
        'PySide6', 
        'ultralytics',
        'opencv-python',
        'torch',
        'numpy',
        'PIL'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
                _ = PIL  # 避免未使用变量警告
            elif package == 'opencv-python':
                import cv2
                _ = cv2  # 避免未使用变量警告
            elif package == 'pyinstaller':
                import PyInstaller
                _ = PyInstaller  # 避免未使用变量警告
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请先安装缺少的包")
        return False
    
    print("\n所有依赖检查通过!")
    return True

def build_executable():
    """构建可执行文件"""
    print_step(3, "构建可执行文件")
    
    # 使用spec文件构建
    command = "pyinstaller build_config.spec --clean --noconfirm"
    
    print("开始构建，这可能需要几分钟时间...")
    start_time = time.time()
    
    success = run_command(command, "使用PyInstaller构建可执行文件")
    
    end_time = time.time()
    build_time = end_time - start_time
    
    if success:
        print(f"\n构建完成! 耗时: {build_time:.1f}秒")
        return True
    else:
        print("\n构建失败!")
        return False

def check_output():
    """检查输出文件"""
    print_step(4, "检查输出文件")
    
    exe_path = Path("dist/YOLO目标检测系统.exe")
    
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        print(f"✓ 可执行文件已生成: {exe_path}")
        print(f"✓ 文件大小: {file_size:.1f} MB")
        
        # 检查dist目录内容
        dist_path = Path("dist")
        if dist_path.exists():
            print(f"\ndist目录内容:")
            for item in dist_path.iterdir():
                if item.is_file():
                    size = item.stat().st_size / (1024 * 1024)
                    print(f"  {item.name} ({size:.1f} MB)")
                else:
                    print(f"  {item.name}/ (目录)")
        
        return True
    else:
        print("✗ 可执行文件未找到")
        return False

def create_readme():
    """创建使用说明"""
    print_step(5, "创建使用说明")
    
    readme_content = """# YOLO目标检测系统 - 可执行文件

## 文件说明
- YOLO目标检测系统.exe: 主程序可执行文件

## 使用方法
1. 双击 "YOLO目标检测系统.exe" 启动程序
2. 程序启动后会显示图形界面
3. 按照界面提示进行目标检测操作

## 系统要求
- Windows 10/11 (64位)
- 至少4GB内存
- 支持OpenGL的显卡(推荐)

## 注意事项
- 首次运行可能需要较长时间启动
- 如果遇到问题，请检查Windows防火墙和杀毒软件设置
- 程序运行时会自动下载YOLO模型文件(如果不存在)

## 支持的功能
- 图像目标检测
- 视频目标检测
- 实时摄像头检测
- 多种YOLO模型支持
- 模型格式转换(ONNX, NCNN, OpenVINO)
- 检测结果导出

## 技术支持
如有问题请联系开发者
"""
    
    readme_path = Path("dist/README.txt")
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✓ 使用说明已创建: {readme_path}")
        return True
    except Exception as e:
        print(f"✗ 创建使用说明失败: {e}")
        return False

def main():
    """主函数"""
    print("YOLO目标检测系统 - Windows可执行文件构建脚本")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查是否在正确的目录
    if not Path("main.py").exists():
        print("错误: 请在项目根目录运行此脚本")
        return False
    
    # 执行构建步骤
    steps = [
        clean_build_dirs,
        check_dependencies, 
        build_executable,
        check_output,
        create_readme
    ]
    
    for step_func in steps:
        if not step_func():
            print(f"\n构建失败于步骤: {step_func.__name__}")
            return False
    
    print_step("完成", "构建成功!")
    print("\n可执行文件位置: dist/YOLO目标检测系统.exe")
    print("您可以将dist目录中的文件分发给其他用户")
    
    return True

if __name__ == "__main__":
    success = main()
    input(f"\n按回车键退出... (构建{'成功' if success else '失败'})")
