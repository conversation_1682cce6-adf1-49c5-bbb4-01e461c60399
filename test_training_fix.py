#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练模块修复的脚本
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.trainer import YOLOTrainer
from core.csv_monitor import CombinedTrainingMonitor

def test_training_monitor():
    """测试训练监控器"""
    print("=" * 60)
    print("开始测试训练监控器修复")
    print("=" * 60)
    
    # 创建训练器
    trainer = YOLOTrainer()
    
    # 加载模型 - 使用用户指定的分割模型
    model_path = "models/YOLOv8n-seg.pt"
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在 {model_path}")
        return False

    print(f"加载模型: {model_path}")
    success = trainer.load_custom_model(model_path)
    if not success:
        print("模型加载失败")
        return False

    print("✓ 模型加载成功")

    # 检查数据集 - 使用用户的实际数据集
    data_config = "datasets/data.yaml"
    if not os.path.exists(data_config):
        print(f"错误: 数据配置文件不存在 {data_config}")
        return False

    print(f"使用数据集: {data_config}")

    # 设置训练配置 - 使用用户指定的参数
    training_config = {
        'epochs': 3,      # 训练3个轮次用于测试实时更新
        'batch': 6,       # 用户指定的批次大小
        'workers': 0,     # 用户指定的workers数量
        'imgsz': 640,     # 标准图像尺寸
        'device': 'cpu',  # 使用CPU避免GPU问题
        'cache': False,
        'plots': True,
        'verbose': True,
        'save': True,
        'save_period': 1  # 每个轮次都保存
    }
    
    trainer.set_training_config(training_config)
    print(f"训练配置: {training_config}")
    
    # 创建回调函数来测试监控
    progress_updates = []
    log_messages = []
    metrics_updates = []
    batch_progress_updates = []
    
    def progress_callback(progress, message):
        progress_updates.append((progress, message))
        print(f"[PROGRESS] {progress}% - {message}")
    
    def log_callback(message):
        log_messages.append(message)
        # 避免递归，直接写入标准输出
        import sys
        sys.__stdout__.write(f"[LOG] {message}\n")
        sys.__stdout__.flush()
    
    def metrics_callback(metrics):
        metrics_updates.append(metrics)
        print(f"[METRICS] {metrics}")
    
    def batch_progress_callback(progress, message):
        batch_progress_updates.append((progress, message))
        print(f"[BATCH] {progress}% - {message}")
    
    print("\n" + "=" * 60)
    print("开始训练测试...")
    print("=" * 60)
    
    # 开始训练
    start_time = time.time()
    success = trainer.train(
        data_config,
        progress_callback=progress_callback,
        log_callback=log_callback,
        metrics_callback=metrics_callback,
        batch_progress_callback=batch_progress_callback
    )
    
    end_time = time.time()
    training_time = end_time - start_time
    
    print("\n" + "=" * 60)
    print("训练测试完成")
    print("=" * 60)
    
    print(f"训练结果: {'成功' if success else '失败'}")
    print(f"训练时间: {training_time:.2f}秒")
    print(f"进度更新次数: {len(progress_updates)}")
    print(f"日志消息数量: {len(log_messages)}")
    print(f"指标更新次数: {len(metrics_updates)}")
    print(f"批次进度更新次数: {len(batch_progress_updates)}")
    
    # 显示一些示例输出
    if progress_updates:
        print("\n最近的进度更新:")
        for i, (progress, message) in enumerate(progress_updates[-3:]):
            print(f"  {i+1}. {progress}% - {message}")
    
    if metrics_updates:
        print("\n最近的指标更新:")
        for i, metrics in enumerate(metrics_updates[-2:]):
            print(f"  {i+1}. {metrics}")
    
    if batch_progress_updates:
        print("\n最近的批次进度更新:")
        for i, (progress, message) in enumerate(batch_progress_updates[-3:]):
            print(f"  {i+1}. {progress}% - {message}")
    
    # 检查exp文件夹
    print("\n检查生成的文件:")
    runs_dir = Path("runs/train")
    if runs_dir.exists():
        exp_dirs = list(runs_dir.glob("exp*"))
        if exp_dirs:
            latest_exp = max(exp_dirs, key=lambda x: x.stat().st_mtime)
            print(f"最新的exp文件夹: {latest_exp}")
            
            results_csv = latest_exp / "results.csv"
            if results_csv.exists():
                print(f"✓ results.csv文件存在: {results_csv}")
                # 读取CSV文件的前几行
                try:
                    with open(results_csv, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    print(f"CSV文件行数: {len(lines)}")
                    if len(lines) > 1:
                        print("CSV文件头部:")
                        for i, line in enumerate(lines[:3]):
                            print(f"  {i+1}. {line.strip()}")
                except Exception as e:
                    print(f"读取CSV文件失败: {e}")
            else:
                print("✗ results.csv文件不存在")
        else:
            print("✗ 没有找到exp文件夹")
    else:
        print("✗ runs/train目录不存在")
    
    return success

def main():
    """主函数"""
    try:
        # 设置环境变量避免重复库问题
        os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
        
        success = test_training_monitor()
        
        if success:
            print("\n" + "=" * 60)
            print("✓ 训练监控器测试成功!")
            print("修复验证:")
            print("1. 终端输出捕获 - 正常工作")
            print("2. 进度更新 - 正常工作") 
            print("3. CSV文件监控 - 正常工作")
            print("4. 批次进度更新 - 正常工作")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("✗ 训练监控器测试失败!")
            print("需要进一步调试")
            print("=" * 60)
            
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
