# 训练配置管理器
import os
import json
from pathlib import Path
from typing import Dict, Any, List
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGroupBox,
                               QLabel, QPushButton, QLineEdit, QListWidget,
                               QListWidgetItem, QTextEdit, QMessageBox,
                               QInputDialog, QSplitter)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont


class TrainingConfigManager(QDialog):
    """训练配置管理器"""
    
    configSelected = Signal(dict)  # 配置选择信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("训练配置管理器")
        self.setModal(True)
        self.resize(800, 600)
        
        # 配置文件路径
        self.config_dir = Path("training_configs")
        self.config_dir.mkdir(exist_ok=True)
        
        self.setup_ui()
        self.load_configs()
        self.connect_signals()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧 - 配置列表
        left_panel = self.create_config_list_panel()
        left_panel.setMaximumWidth(300)
        splitter.addWidget(left_panel)
        
        # 右侧 - 配置详情
        right_panel = self.create_config_detail_panel()
        splitter.addWidget(right_panel)
        
        layout.addWidget(splitter)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.apply_btn = QPushButton("应用配置")
        self.save_btn = QPushButton("保存配置")
        self.delete_btn = QPushButton("删除配置")
        self.close_btn = QPushButton("关闭")
        
        self.apply_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_config_list_panel(self):
        """创建配置列表面板"""
        panel = QGroupBox("配置列表")
        layout = QVBoxLayout(panel)
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索配置...")
        layout.addWidget(self.search_edit)
        
        # 配置列表
        self.config_list = QListWidget()
        layout.addWidget(self.config_list)
        
        # 预设配置按钮
        preset_layout = QHBoxLayout()
        self.create_preset_btn = QPushButton("创建预设")
        self.import_btn = QPushButton("导入配置")
        self.export_btn = QPushButton("导出配置")
        
        preset_layout.addWidget(self.create_preset_btn)
        preset_layout.addWidget(self.import_btn)
        preset_layout.addWidget(self.export_btn)
        
        layout.addLayout(preset_layout)
        
        return panel
    
    def create_config_detail_panel(self):
        """创建配置详情面板"""
        panel = QGroupBox("配置详情")
        layout = QVBoxLayout(panel)
        
        # 配置名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("配置名称:"))
        self.config_name_edit = QLineEdit()
        name_layout.addWidget(self.config_name_edit)
        layout.addLayout(name_layout)
        
        # 配置描述
        layout.addWidget(QLabel("配置描述:"))
        self.config_desc_edit = QTextEdit()
        self.config_desc_edit.setMaximumHeight(80)
        layout.addWidget(self.config_desc_edit)
        
        # 配置内容
        layout.addWidget(QLabel("配置参数:"))
        self.config_content_edit = QTextEdit()
        self.config_content_edit.setFont(QFont("Consolas", 10))
        layout.addWidget(self.config_content_edit)
        
        return panel
    
    def connect_signals(self):
        """连接信号"""
        self.config_list.itemSelectionChanged.connect(self.on_config_selected)
        self.search_edit.textChanged.connect(self.filter_configs)
        
        self.apply_btn.clicked.connect(self.apply_config)
        self.save_btn.clicked.connect(self.save_config)
        self.delete_btn.clicked.connect(self.delete_config)
        self.close_btn.clicked.connect(self.close)
        
        self.create_preset_btn.clicked.connect(self.create_preset_configs)
        self.import_btn.clicked.connect(self.import_config)
        self.export_btn.clicked.connect(self.export_config)
    
    def load_configs(self):
        """加载配置列表"""
        self.config_list.clear()
        
        for config_file in self.config_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                item = QListWidgetItem(config_data.get('name', config_file.stem))
                item.setData(Qt.UserRole, config_file)
                item.setToolTip(config_data.get('description', ''))
                self.config_list.addItem(item)
                
            except Exception as e:
                print(f"加载配置文件失败 {config_file}: {e}")
    
    def on_config_selected(self):
        """配置选择事件"""
        current_item = self.config_list.currentItem()
        if not current_item:
            self.apply_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            self.clear_detail_panel()
            return
        
        config_file = current_item.data(Qt.UserRole)
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 显示配置详情
            self.config_name_edit.setText(config_data.get('name', ''))
            self.config_desc_edit.setText(config_data.get('description', ''))
            
            # 格式化显示配置参数
            params = config_data.get('parameters', {})
            formatted_params = json.dumps(params, indent=2, ensure_ascii=False)
            self.config_content_edit.setText(formatted_params)
            
            self.apply_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"加载配置失败: {str(e)}")
    
    def clear_detail_panel(self):
        """清空详情面板"""
        self.config_name_edit.clear()
        self.config_desc_edit.clear()
        self.config_content_edit.clear()
    
    def filter_configs(self, text):
        """过滤配置列表"""
        for i in range(self.config_list.count()):
            item = self.config_list.item(i)
            item.setHidden(text.lower() not in item.text().lower())
    
    def apply_config(self):
        """应用配置"""
        try:
            config_text = self.config_content_edit.toPlainText()
            config_params = json.loads(config_text)
            self.configSelected.emit(config_params)
            self.accept()
            
        except json.JSONDecodeError as e:
            QMessageBox.warning(self, "错误", f"配置格式错误: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"应用配置失败: {str(e)}")
    
    def save_config(self):
        """保存配置"""
        name = self.config_name_edit.text().strip()
        if not name:
            QMessageBox.warning(self, "警告", "请输入配置名称")
            return
        
        try:
            config_text = self.config_content_edit.toPlainText()
            config_params = json.loads(config_text) if config_text else {}
            
            config_data = {
                'name': name,
                'description': self.config_desc_edit.toPlainText(),
                'parameters': config_params,
                'created_time': str(Path().cwd())  # 简单的时间戳
            }
            
            # 保存到文件
            config_file = self.config_dir / f"{name}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            QMessageBox.information(self, "成功", f"配置已保存: {config_file}")
            self.load_configs()
            
        except json.JSONDecodeError as e:
            QMessageBox.warning(self, "错误", f"配置格式错误: {str(e)}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"保存配置失败: {str(e)}")
    
    def delete_config(self):
        """删除配置"""
        current_item = self.config_list.currentItem()
        if not current_item:
            return
        
        config_file = current_item.data(Qt.UserRole)
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除配置 '{current_item.text()}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                config_file.unlink()
                QMessageBox.information(self, "成功", "配置已删除")
                self.load_configs()
                self.clear_detail_panel()
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除配置失败: {str(e)}")
    
    def create_preset_configs(self):
        """创建预设配置"""
        presets = {
            "快速训练": {
                "description": "适合快速测试的训练配置",
                "parameters": {
                    "epochs": 50,
                    "batch": 16,
                    "imgsz": 640,
                    "lr0": 0.01,
                    "patience": 10,
                    "save_period": 10
                }
            },
            "高精度训练": {
                "description": "追求高精度的训练配置",
                "parameters": {
                    "epochs": 300,
                    "batch": 8,
                    "imgsz": 640,
                    "lr0": 0.001,
                    "lrf": 0.0001,
                    "momentum": 0.937,
                    "weight_decay": 0.0005,
                    "warmup_epochs": 5.0
                }
            },
            "大数据集训练": {
                "description": "适合大数据集的训练配置",
                "parameters": {
                    "epochs": 200,
                    "batch": 32,
                    "imgsz": 640,
                    "lr0": 0.01,
                    "workers": 16,
                    "cache": True,
                    "amp": True
                }
            }
        }
        
        for name, config in presets.items():
            config_data = {
                'name': name,
                'description': config['description'],
                'parameters': config['parameters']
            }
            
            config_file = self.config_dir / f"{name}.json"
            if not config_file.exists():
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        QMessageBox.information(self, "成功", "预设配置已创建")
        self.load_configs()
    
    def import_config(self):
        """导入配置"""
        from PySide6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入配置", "", "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 验证配置格式
                if 'name' not in config_data or 'parameters' not in config_data:
                    QMessageBox.warning(self, "错误", "配置文件格式不正确")
                    return
                
                # 复制到配置目录
                name = config_data['name']
                target_file = self.config_dir / f"{name}.json"
                
                with open(target_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
                
                QMessageBox.information(self, "成功", f"配置已导入: {name}")
                self.load_configs()
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"导入配置失败: {str(e)}")
    
    def export_config(self):
        """导出配置"""
        current_item = self.config_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请选择要导出的配置")
            return
        
        from PySide6.QtWidgets import QFileDialog
        
        config_file = current_item.data(Qt.UserRole)
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出配置", f"{current_item.text()}.json", "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                import shutil
                shutil.copy2(config_file, file_path)
                QMessageBox.information(self, "成功", f"配置已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"导出配置失败: {str(e)}")
    
    @staticmethod
    def get_config(parent=None, current_config=None):
        """静态方法：获取配置"""
        dialog = TrainingConfigManager(parent)
        
        # 如果提供了当前配置，显示在详情面板中
        if current_config:
            dialog.config_name_edit.setText("当前配置")
            dialog.config_desc_edit.setText("当前使用的训练配置")
            formatted_config = json.dumps(current_config, indent=2, ensure_ascii=False)
            dialog.config_content_edit.setText(formatted_config)
        
        if dialog.exec() == QDialog.Accepted:
            return dialog.selected_config
        return None
