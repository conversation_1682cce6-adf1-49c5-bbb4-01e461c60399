# YOLO模型训练界面组件
import os
import sys
from pathlib import Path
from typing import Dict, Any, List
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                               QLabel, QPushButton, QLineEdit, QSpinBox, QDoubleSpinBox,
                               QComboBox, QCheckBox, QTextEdit, QProgressBar,
                               QFileDialog, QMessageBox, QTabWidget, QFormLayout,
                               QScrollArea, QSplitter, QListWidget, QListWidgetItem)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont

from core.trainer import YOLOTrainer

from .training_config_manager import TrainingConfigManager


class ModelDownloadThread(QThread):
    """模型下载线程"""
    
    progressUpdate = Signal(int, str)  # 进度和状态消息
    finished = Signal(bool, str)  # 完成状态和消息
    
    def __init__(self, trainer, model_name, save_dir):
        super().__init__()
        self.trainer = trainer
        self.model_name = model_name
        self.save_dir = save_dir
    
    def run(self):
        """运行下载"""
        try:
            success = self.trainer.download_pretrained_model(
                self.model_name, 
                self.save_dir,
                self.progress_callback
            )
            
            if success:
                # 加载下载的模型
                model_loaded = self.trainer.load_pretrained_model(self.model_name, self.save_dir)
                if model_loaded:
                    self.finished.emit(True, f"模型 {self.model_name} 下载并加载成功")
                else:
                    self.finished.emit(False, f"模型下载成功但加载失败")
            else:
                self.finished.emit(False, f"模型 {self.model_name} 下载失败")
                
        except Exception as e:
            self.finished.emit(False, f"下载过程出错: {str(e)}")
    
    def progress_callback(self, progress, message):
        """进度回调"""
        self.progressUpdate.emit(progress, message)


class TerminalCapture:
    """终端输出捕获器 - 简化版本，避免递归问题"""
    def __init__(self, log_callback, update_callback=None):
        self.log_callback = log_callback  # 日志回调
        self.update_callback = update_callback  # 动态更新回调（暂时不使用）
        self.original_stdout = None
        self.original_stderr = None
        self.line_buffer = ""  # 行缓冲区

    def start_capture(self):
        """开始捕获终端输出"""
        import sys
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        sys.stdout = self
        sys.stderr = self

    def stop_capture(self):
        """停止捕获终端输出"""
        import sys
        if self.original_stdout:
            sys.stdout = self.original_stdout
        if self.original_stderr:
            sys.stderr = self.original_stderr

    def write(self, text):
        """重写write方法，捕获输出 - 简化版本"""
        # 写入原始输出（保持终端显示）
        if self.original_stdout:
            self.original_stdout.write(text)
            self.original_stdout.flush()

        # 简化的输出处理，避免递归问题
        if text and self.log_callback:
            try:
                # 将文本添加到缓冲区
                self.line_buffer += text

                # 处理完整的行（以\n结尾）
                while '\n' in self.line_buffer:
                    line, self.line_buffer = self.line_buffer.split('\n', 1)
                    if line.strip():
                        # 清理ANSI转义序列
                        import re
                        clean_line = re.sub(r'\x1b\[[0-9;]*m', '', line)
                        clean_line = clean_line.strip()

                        if clean_line:
                            # 检查是否是进度行
                            if '%|' in clean_line or 'it/s' in clean_line:
                                # 进度行，使用动态更新回调
                                if self.update_callback:
                                    self.update_callback(clean_line)
                                else:
                                    self.log_callback(clean_line)
                            else:
                                # 普通行，使用静态回调
                                self.log_callback(clean_line)
            except:
                # 如果出现任何错误，静默忽略，避免递归
                pass

        return len(text)

    def flush(self):
        """刷新输出"""
        if self.original_stdout:
            self.original_stdout.flush()

        # 处理缓冲区中剩余的内容
        if self.line_buffer.strip():
            try:
                # 清理ANSI转义序列
                import re
                clean_line = re.sub(r'\x1b\[[0-9;]*m', '', self.line_buffer)
                clean_line = clean_line.strip()

                if clean_line and self.log_callback:
                    self.log_callback(clean_line)

                self.line_buffer = ""
            except:
                pass




class TrainingThread(QThread):
    """训练线程"""

    progressUpdate = Signal(int, str)  # 进度和状态消息
    logUpdate = Signal(str)  # 日志消息
    finished = Signal(bool, str)  # 完成状态和消息

    def __init__(self, trainer, data_config):
        super().__init__()
        self.trainer = trainer
        self.data_config = data_config
        self._stop_requested = False
        self.terminal_capture = None
    
    def run(self):
        """运行训练"""
        try:
            # 简化的训练调用，使用原始的log_callback
            success = self.trainer.train(
                self.data_config,
                self.progress_callback,
                self.log_callback  # 使用原始的log_callback
            )

            if success:
                self.finished.emit(True, "训练完成")
            else:
                self.finished.emit(False, "训练失败")

        except Exception as e:
            self.finished.emit(False, f"训练过程出错: {str(e)}")

    def stop_training(self):
        """停止训练"""
        self._stop_requested = True
        if self.trainer:
            self.trainer.is_training = False
            # 强制停止训练器的监控循环
            self.trainer.stop_training()

    def progress_callback(self, progress, message):
        """进度回调"""
        if not self._stop_requested:
            self.progressUpdate.emit(progress, message)

    def log_callback(self, message):
        """日志回调"""
        if not self._stop_requested:
            self.logUpdate.emit(message)






class TrainingWidget(QWidget):
    """训练界面组件"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化训练器
        self.trainer = YOLOTrainer()
        self.download_thread = None
        self.training_thread = None
        
        # 当前数据集路径
        self.current_dataset = None
        self.current_data_config = None
        
        self.setup_ui()
        self.connect_signals()
        self.load_pretrained_models()
    
    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧面板 - 配置
        left_panel = self.create_config_panel()
        left_panel.setMaximumWidth(400)
        left_panel.setMinimumWidth(350)
        
        # 右侧面板 - 日志和进度
        right_panel = self.create_log_panel()
        
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([400, 600])
        
        layout.addWidget(splitter)
    
    def create_config_panel(self) -> QWidget:
        """创建配置面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 模型选择标签页
        model_tab = self.create_model_tab()
        tab_widget.addTab(model_tab, "模型选择")
        
        # 数据集标签页
        dataset_tab = self.create_dataset_tab()
        tab_widget.addTab(dataset_tab, "数据集")
        
        # 训练参数标签页
        params_tab = self.create_params_tab()
        tab_widget.addTab(params_tab, "训练参数")
        
        layout.addWidget(tab_widget)
        
        # 训练控制按钮
        control_group = QGroupBox("训练控制")
        control_layout = QVBoxLayout(control_group)
        
        self.start_training_btn = QPushButton("开始训练")
        self.stop_training_btn = QPushButton("停止训练")
        self.force_stop_btn = QPushButton("强制停止")
        self.force_stop_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold;")
        self.force_stop_btn.setToolTip("强制终止训练进程（可能导致数据丢失）")
        self.force_stop_btn.setVisible(False)  # 默认隐藏
        self.validate_model_btn = QPushButton("验证模型")
        
        self.start_training_btn.setEnabled(False)
        self.stop_training_btn.setEnabled(False)
        self.validate_model_btn.setEnabled(False)
        
        control_layout.addWidget(self.start_training_btn)
        control_layout.addWidget(self.stop_training_btn)
        control_layout.addWidget(self.force_stop_btn)
        control_layout.addWidget(self.validate_model_btn)
        
        layout.addWidget(control_group)
        
        return panel
    
    def create_model_tab(self) -> QWidget:
        """创建模型选择标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 预训练模型组
        pretrained_group = QGroupBox("预训练模型")
        pretrained_layout = QVBoxLayout(pretrained_group)
        
        self.model_combo = QComboBox()

        # 下载按钮组
        download_layout = QHBoxLayout()
        self.download_model_btn = QPushButton("自动下载")
        self.browser_download_btn = QPushButton("浏览器下载")
        self.browser_download_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.browser_download_btn.setToolTip("在浏览器中打开下载链接（推荐）")

        download_layout.addWidget(self.download_model_btn)
        download_layout.addWidget(self.browser_download_btn)

        self.model_status_label = QLabel("未选择模型")
        self.model_status_label.setStyleSheet("color: gray;")

        pretrained_layout.addWidget(QLabel("选择预训练模型:"))
        pretrained_layout.addWidget(self.model_combo)
        pretrained_layout.addLayout(download_layout)
        pretrained_layout.addWidget(self.model_status_label)
        
        layout.addWidget(pretrained_group)
        
        # 自定义模型组
        custom_group = QGroupBox("自定义模型")
        custom_layout = QVBoxLayout(custom_group)
        
        self.custom_model_path = QLineEdit()
        self.custom_model_path.setPlaceholderText("选择自定义模型文件...")
        self.browse_model_btn = QPushButton("浏览")
        self.load_custom_btn = QPushButton("加载自定义模型")
        
        custom_model_layout = QHBoxLayout()
        custom_model_layout.addWidget(self.custom_model_path)
        custom_model_layout.addWidget(self.browse_model_btn)

        # 添加检查下载按钮
        self.check_downloads_btn = QPushButton("检查下载")
        self.check_downloads_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.check_downloads_btn.setToolTip("检查Downloads目录中的模型文件")
        custom_model_layout.addWidget(self.check_downloads_btn)
        
        custom_layout.addWidget(QLabel("自定义模型路径:"))
        custom_layout.addLayout(custom_model_layout)
        custom_layout.addWidget(self.load_custom_btn)
        
        layout.addWidget(custom_group)
        
        # 模型信息
        info_group = QGroupBox("模型信息")
        info_layout = QVBoxLayout(info_group)
        
        self.model_info_text = QTextEdit()
        self.model_info_text.setMaximumHeight(100)
        self.model_info_text.setReadOnly(True)
        self.model_info_text.setPlaceholderText("模型信息将在这里显示...")
        
        info_layout.addWidget(self.model_info_text)
        layout.addWidget(info_group)
        
        return widget
    
    def create_dataset_tab(self) -> QWidget:
        """创建数据集标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 数据集路径组
        dataset_group = QGroupBox("数据集路径")
        dataset_layout = QVBoxLayout(dataset_group)
        
        self.dataset_path = QLineEdit()
        self.dataset_path.setPlaceholderText("选择数据集目录...")
        self.browse_dataset_btn = QPushButton("浏览")
        self.validate_dataset_btn = QPushButton("验证数据集")
        
        dataset_path_layout = QHBoxLayout()
        dataset_path_layout.addWidget(self.dataset_path)
        dataset_path_layout.addWidget(self.browse_dataset_btn)
        
        dataset_layout.addWidget(QLabel("数据集目录:"))
        dataset_layout.addLayout(dataset_path_layout)
        dataset_layout.addWidget(self.validate_dataset_btn)
        
        layout.addWidget(dataset_group)
        
        # 数据集信息
        info_group = QGroupBox("数据集信息")
        info_layout = QVBoxLayout(info_group)
        
        self.dataset_info_text = QTextEdit()
        self.dataset_info_text.setMaximumHeight(150)
        self.dataset_info_text.setReadOnly(True)
        self.dataset_info_text.setPlaceholderText("数据集信息将在这里显示...")
        
        info_layout.addWidget(self.dataset_info_text)
        layout.addWidget(info_group)

        return widget

    def create_params_tab(self) -> QWidget:
        """创建训练参数标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 创建滚动区域
        scroll = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QFormLayout(scroll_widget)

        # 基础参数
        basic_group = QGroupBox("基础参数")
        basic_layout = QFormLayout(basic_group)

        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 1000)
        self.epochs_spin.setValue(100)
        basic_layout.addRow("训练轮数 (epochs):", self.epochs_spin)

        self.batch_spin = QSpinBox()
        self.batch_spin.setRange(1, 128)
        self.batch_spin.setValue(16)
        basic_layout.addRow("批次大小 (batch):", self.batch_spin)

        self.imgsz_spin = QSpinBox()
        self.imgsz_spin.setRange(320, 1280)
        self.imgsz_spin.setValue(640)
        self.imgsz_spin.setSingleStep(32)
        basic_layout.addRow("图像尺寸 (imgsz):", self.imgsz_spin)

        self.device_combo = QComboBox()
        self.device_combo.addItems(['auto', 'cpu', 'cuda'])
        basic_layout.addRow("设备 (device):", self.device_combo)

        layout.addWidget(basic_group)

        # 学习率参数
        lr_group = QGroupBox("学习率参数")
        lr_layout = QFormLayout(lr_group)

        self.lr0_spin = QDoubleSpinBox()
        self.lr0_spin.setRange(0.0001, 1.0)
        self.lr0_spin.setValue(0.01)
        self.lr0_spin.setDecimals(4)
        self.lr0_spin.setSingleStep(0.001)
        lr_layout.addRow("初始学习率 (lr0):", self.lr0_spin)

        self.lrf_spin = QDoubleSpinBox()
        self.lrf_spin.setRange(0.0001, 1.0)
        self.lrf_spin.setValue(0.01)
        self.lrf_spin.setDecimals(4)
        self.lrf_spin.setSingleStep(0.001)
        lr_layout.addRow("最终学习率 (lrf):", self.lrf_spin)

        self.momentum_spin = QDoubleSpinBox()
        self.momentum_spin.setRange(0.0, 1.0)
        self.momentum_spin.setValue(0.937)
        self.momentum_spin.setDecimals(3)
        self.momentum_spin.setSingleStep(0.01)
        lr_layout.addRow("动量 (momentum):", self.momentum_spin)

        self.weight_decay_spin = QDoubleSpinBox()
        self.weight_decay_spin.setRange(0.0, 0.01)
        self.weight_decay_spin.setValue(0.0005)
        self.weight_decay_spin.setDecimals(4)
        self.weight_decay_spin.setSingleStep(0.0001)
        lr_layout.addRow("权重衰减 (weight_decay):", self.weight_decay_spin)

        layout.addWidget(lr_group)

        # 高级参数
        advanced_group = QGroupBox("高级参数")
        advanced_layout = QFormLayout(advanced_group)

        self.warmup_epochs_spin = QDoubleSpinBox()
        self.warmup_epochs_spin.setRange(0.0, 10.0)
        self.warmup_epochs_spin.setValue(3.0)
        self.warmup_epochs_spin.setDecimals(1)
        advanced_layout.addRow("预热轮数 (warmup_epochs):", self.warmup_epochs_spin)

        self.workers_spin = QSpinBox()
        self.workers_spin.setRange(0, 16)
        self.workers_spin.setValue(8)
        advanced_layout.addRow("工作进程数 (workers):", self.workers_spin)

        self.optimizer_combo = QComboBox()
        self.optimizer_combo.addItems(['auto', 'SGD', 'Adam', 'AdamW', 'RMSProp'])
        advanced_layout.addRow("优化器 (optimizer):", self.optimizer_combo)

        # 布尔参数
        self.amp_check = QCheckBox("混合精度训练 (amp)")
        self.amp_check.setChecked(True)
        advanced_layout.addRow(self.amp_check)

        self.cache_check = QCheckBox("缓存图像 (cache)")
        self.cache_check.setChecked(False)
        advanced_layout.addRow(self.cache_check)

        self.plots_check = QCheckBox("生成训练图表 (plots)")
        self.plots_check.setChecked(True)
        advanced_layout.addRow(self.plots_check)

        layout.addWidget(advanced_group)

        # 配置管理组
        config_group = QGroupBox("配置管理")
        config_layout = QHBoxLayout(config_group)

        self.load_config_btn = QPushButton("加载配置")
        self.save_config_btn = QPushButton("保存配置")
        self.reset_config_btn = QPushButton("重置默认")

        config_layout.addWidget(self.load_config_btn)
        config_layout.addWidget(self.save_config_btn)
        config_layout.addWidget(self.reset_config_btn)

        layout.addWidget(config_group)

        return widget

    def create_log_panel(self) -> QWidget:
        """创建日志面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # 创建标签页
        tab_widget = QTabWidget()

        # 移除训练进度标签页，简化界面

        # 日志标签页
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))

        # 日志控制按钮
        log_control_layout = QHBoxLayout()
        self.clear_log_btn = QPushButton("清空日志")
        self.save_log_btn = QPushButton("保存日志")

        log_control_layout.addWidget(self.clear_log_btn)
        log_control_layout.addWidget(self.save_log_btn)
        log_control_layout.addStretch()

        log_layout.addWidget(self.log_text)
        log_layout.addLayout(log_control_layout)

        tab_widget.addTab(log_widget, "训练日志")

        layout.addWidget(tab_widget)

        # 添加进度条和标签
        progress_layout = QVBoxLayout()
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("就绪")

        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)
        layout.addLayout(progress_layout)

        return panel

    def connect_signals(self):
        """连接信号"""
        # 模型相关
        self.download_model_btn.clicked.connect(self.download_model)
        self.browser_download_btn.clicked.connect(self.browser_download_model)
        self.browse_model_btn.clicked.connect(self.browse_custom_model)
        self.check_downloads_btn.clicked.connect(self.check_downloads)
        self.load_custom_btn.clicked.connect(self.load_custom_model)

        # 数据集相关
        self.browse_dataset_btn.clicked.connect(self.browse_dataset)
        self.validate_dataset_btn.clicked.connect(self.validate_dataset)

        # 训练控制
        self.start_training_btn.clicked.connect(self.start_training)
        self.stop_training_btn.clicked.connect(self.stop_training)
        self.force_stop_btn.clicked.connect(self.force_stop_training)
        self.validate_model_btn.clicked.connect(self.validate_model)

        # 日志控制
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.save_log_btn.clicked.connect(self.save_log)

        # 配置管理
        self.load_config_btn.clicked.connect(self.load_training_config)
        self.save_config_btn.clicked.connect(self.save_training_config)
        self.reset_config_btn.clicked.connect(self.reset_training_config)

    def load_pretrained_models(self):
        """加载预训练模型列表"""
        models = self.trainer.get_pretrained_models()
        self.model_combo.clear()
        self.model_combo.addItems(list(models.keys()))

    def download_model(self):
        """下载预训练模型"""
        model_name = self.model_combo.currentText()
        if not model_name:
            QMessageBox.warning(self, "警告", "请选择要下载的模型")
            return

        # 创建models目录
        models_dir = "models"
        os.makedirs(models_dir, exist_ok=True)

        # 开始下载
        self.download_model_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_label.setText("正在下载模型...")

        self.download_thread = ModelDownloadThread(self.trainer, model_name, models_dir)
        self.download_thread.progressUpdate.connect(self.on_download_progress)
        self.download_thread.finished.connect(self.on_download_finished)
        self.download_thread.start()

    def on_download_progress(self, progress, message):
        """下载进度更新"""
        if progress >= 0:
            self.progress_bar.setValue(progress)
        self.progress_label.setText(message)

    def on_download_finished(self, success, message):
        """下载完成"""
        self.download_model_btn.setEnabled(True)

        if success:
            self.model_status_label.setText(f"模型已加载: {self.model_combo.currentText()}")
            self.model_status_label.setStyleSheet("color: green;")
            self.update_model_info()
            self.update_training_buttons()
            self.add_log(f"✓ {message}")
        else:
            self.model_status_label.setText("模型加载失败")
            self.model_status_label.setStyleSheet("color: red;")
            self.add_log(f"✗ {message}")

            # 检查是否是网络问题
            if any(keyword in message for keyword in ["代理错误", "SSL错误", "连接失败", "ProxyError", "SSLError", "ConnectionError"]):
                self.show_offline_solution()

        self.progress_bar.setValue(0)
        self.progress_label.setText("就绪")

    def show_offline_solution(self):
        """显示离线解决方案"""
        offline_dialog = QMessageBox(self)
        offline_dialog.setWindowTitle("网络连接问题")
        offline_dialog.setIcon(QMessageBox.Information)

        message = """由于网络连接问题，无法自动下载模型。

🔧 解决方案：
1. 手动下载模型文件到 'models' 目录
2. 使用"加载自定义模型"功能

📥 常用模型下载地址：
• YOLOv8n: https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n.pt
• YOLOv8s: https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s.pt

📖 详细说明请查看：
• 离线模型使用指南.md
• models/README.md

是否现在打开相关文件？"""

        offline_dialog.setText(message)
        offline_dialog.addButton("打开models目录", QMessageBox.AcceptRole)
        offline_dialog.addButton("查看使用指南", QMessageBox.ActionRole)
        offline_dialog.addButton("关闭", QMessageBox.RejectRole)

        result = offline_dialog.exec()

        if result == 0:  # 打开models目录
            self.open_models_directory()
        elif result == 1:  # 查看使用指南
            self.open_offline_guide()

    def open_models_directory(self):
        """打开models目录"""
        import os
        import subprocess
        models_path = os.path.abspath("models")
        if not os.path.exists(models_path):
            os.makedirs(models_path, exist_ok=True)

        try:
            subprocess.Popen(f'explorer "{models_path}"')
            self.add_log(f"✓ 已打开models目录: {models_path}")
        except Exception as e:
            self.add_log(f"✗ 无法打开目录: {str(e)}")

    def open_offline_guide(self):
        """打开离线使用指南"""
        import os
        import subprocess
        guide_path = os.path.abspath("离线模型使用指南.md")

        try:
            if os.path.exists(guide_path):
                subprocess.Popen(f'notepad "{guide_path}"')
                self.add_log(f"✓ 已打开使用指南: {guide_path}")
            else:
                QMessageBox.information(self, "提示", "请查看项目目录中的'离线模型使用指南.md'文件")
        except Exception as e:
            self.add_log(f"✗ 无法打开指南: {str(e)}")

    def browser_download_model(self):
        """使用浏览器下载模型"""
        model_name = self.model_combo.currentText()
        if not model_name:
            QMessageBox.warning(self, "警告", "请先选择要下载的模型")
            return

        # 模型URL映射
        model_urls = {
            'yolov8n': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n.pt',
            'yolov8s': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s.pt',
            'yolov8m': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8m.pt',
            'yolov8l': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8l.pt',
            'yolov8x': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8x.pt',
            'yolov8n-seg': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n-seg.pt',
            'yolov8s-seg': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s-seg.pt',
            'yolov8m-seg': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8m-seg.pt',
            'yolov8l-seg': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8l-seg.pt',
            'yolov8x-seg': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8x-seg.pt',
            'yolov8n-pose': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n-pose.pt',
            'yolov8s-pose': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s-pose.pt',
            'yolov8m-pose': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8m-pose.pt',
            'yolov8l-pose': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8l-pose.pt',
            'yolov8x-pose': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8x-pose.pt',
            'yolov8n-cls': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n-cls.pt',
            'yolov8s-cls': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s-cls.pt',
            'yolov8m-cls': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8m-cls.pt',
            'yolov8l-cls': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8l-cls.pt',
            'yolov8x-cls': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8x-cls.pt',
        }

        if model_name not in model_urls:
            QMessageBox.warning(self, "错误", f"不支持的模型: {model_name}")
            return

        url = model_urls[model_name]

        # 显示下载指导对话框
        self.show_browser_download_dialog(model_name, url)

    def show_browser_download_dialog(self, model_name, url):
        """显示浏览器下载指导对话框"""
        dialog = QMessageBox(self)
        dialog.setWindowTitle("浏览器下载指导")
        dialog.setIcon(QMessageBox.Information)

        message = f"""即将在浏览器中打开 {model_name} 的下载链接。

📥 下载步骤：
1. 浏览器将自动开始下载模型文件
2. 下载完成后，将文件移动到项目的 'models' 目录
3. 返回此界面，使用"加载自定义模型"功能

📁 目标位置：
{os.path.abspath('models')}

🔗 下载链接：
{url}

是否现在打开下载链接？"""

        dialog.setText(message)
        dialog.addButton("打开下载链接", QMessageBox.AcceptRole)
        dialog.addButton("打开models目录", QMessageBox.ActionRole)
        dialog.addButton("取消", QMessageBox.RejectRole)

        result = dialog.exec()

        if result == 0:  # 打开下载链接
            self.open_download_url(model_name, url)
        elif result == 1:  # 打开models目录
            self.open_models_directory()

    def open_download_url(self, model_name, url):
        """在浏览器中打开下载链接"""
        try:
            import webbrowser
            webbrowser.open(url)

            self.add_log(f"🌐 已在浏览器中打开 {model_name} 下载链接")
            self.add_log(f"📁 请将下载的文件保存到: {os.path.abspath('models')}")

            # 创建models目录（如果不存在）
            os.makedirs("models", exist_ok=True)

            # 显示后续操作提示
            QMessageBox.information(
                self, "下载提示",
                f"浏览器下载已开始！\n\n"
                f"下载完成后：\n"
                f"1. 将 {model_name}.pt 文件移动到 'models' 目录\n"
                f"2. 使用下方的'加载自定义模型'功能\n"
                f"3. 选择下载的模型文件"
            )

        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开浏览器: {str(e)}")
            self.add_log(f"✗ 打开浏览器失败: {str(e)}")

    def check_downloads(self):
        """检查Downloads目录中的模型文件"""
        try:
            from pathlib import Path
            downloads_dir = Path.home() / "Downloads"
            models_dir = Path("models")

            # 查找YOLO模型文件
            yolo_files = []
            for pattern in ["yolo*.pt", "yolo*.pth"]:
                yolo_files.extend(downloads_dir.glob(pattern))

            if not yolo_files:
                QMessageBox.information(
                    self, "检查结果",
                    f"在Downloads目录中未找到YOLO模型文件。\n\n"
                    f"搜索路径: {downloads_dir}\n"
                    f"搜索模式: yolo*.pt, yolo*.pth"
                )
                return

            # 显示找到的文件
            file_list = []
            for file in yolo_files:
                size_mb = file.stat().st_size / (1024 * 1024)
                file_list.append(f"• {file.name} ({size_mb:.1f} MB)")

            files_text = "\n".join(file_list)

            reply = QMessageBox.question(
                self, "找到模型文件",
                f"在Downloads目录中找到 {len(yolo_files)} 个YOLO模型文件：\n\n"
                f"{files_text}\n\n"
                f"是否将这些文件移动到models目录？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                self.move_files_to_models(yolo_files)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"检查下载文件失败: {str(e)}")
            self.add_log(f"✗ 检查下载失败: {str(e)}")

    def move_files_to_models(self, files):
        """将文件移动到models目录"""
        try:
            from pathlib import Path
            import shutil

            models_dir = Path("models")
            models_dir.mkdir(exist_ok=True)

            moved_files = []
            skipped_files = []

            for file in files:
                target_path = models_dir / file.name

                if target_path.exists():
                    skipped_files.append(file.name)
                    continue

                try:
                    shutil.move(str(file), str(target_path))
                    moved_files.append(file.name)
                    self.add_log(f"✓ 已移动: {file.name} -> models/")
                except Exception as e:
                    self.add_log(f"✗ 移动失败 {file.name}: {e}")

            # 显示结果
            result_msg = f"文件移动完成！\n\n"
            if moved_files:
                result_msg += f"已移动 {len(moved_files)} 个文件：\n"
                result_msg += "\n".join(f"• {f}" for f in moved_files)
                result_msg += "\n\n"

            if skipped_files:
                result_msg += f"跳过 {len(skipped_files)} 个已存在的文件：\n"
                result_msg += "\n".join(f"• {f}" for f in skipped_files)
                result_msg += "\n\n"

            result_msg += "现在可以使用'加载自定义模型'功能加载这些模型。"

            QMessageBox.information(self, "移动完成", result_msg)

            # 如果有移动的文件，自动选择第一个
            if moved_files:
                first_file = models_dir / moved_files[0]
                self.custom_model_path.setText(str(first_file))
                self.add_log(f"💡 提示: 已自动选择 {moved_files[0]}，可直接点击'加载自定义模型'")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"移动文件失败: {str(e)}")
            self.add_log(f"✗ 移动文件失败: {str(e)}")

    def browse_custom_model(self):
        """浏览自定义模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择模型文件",
            "",
            "PyTorch模型 (*.pt *.pth);;所有文件 (*)"
        )

        if file_path:
            self.custom_model_path.setText(file_path)

    def load_custom_model(self):
        """加载自定义模型"""
        model_path = self.custom_model_path.text().strip()
        if not model_path:
            QMessageBox.warning(self, "警告", "请选择模型文件")
            return

        if not os.path.exists(model_path):
            QMessageBox.warning(self, "警告", "模型文件不存在")
            return

        # 加载模型
        success = self.trainer.load_custom_model(model_path)

        if success:
            self.model_status_label.setText(f"自定义模型已加载: {os.path.basename(model_path)}")
            self.model_status_label.setStyleSheet("color: green;")
            self.update_model_info()
            self.update_training_buttons()
            self.add_log(f"✓ 自定义模型加载成功: {model_path}")
        else:
            self.model_status_label.setText("自定义模型加载失败")
            self.model_status_label.setStyleSheet("color: red;")
            self.add_log(f"✗ 自定义模型加载失败: {model_path}")

    def browse_dataset(self):
        """浏览数据集目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择数据集目录",
            ""
        )

        if dir_path:
            self.dataset_path.setText(dir_path)

    def validate_dataset(self):
        """验证数据集"""
        dataset_path = self.dataset_path.text().strip()
        if not dataset_path:
            QMessageBox.warning(self, "警告", "请选择数据集目录")
            return

        if not os.path.exists(dataset_path):
            QMessageBox.warning(self, "警告", "数据集目录不存在")
            return

        # 验证数据集
        validation_result = self.trainer.validate_dataset(dataset_path)

        # 显示验证结果
        info_text = "数据集验证结果:\n\n"

        if validation_result['valid']:
            info_text += "✓ 数据集格式正确\n\n"
            self.current_dataset = dataset_path
            self.update_training_buttons()
        else:
            info_text += "✗ 数据集格式有问题\n\n"
            self.current_dataset = None

        # 显示错误信息
        if validation_result['errors']:
            info_text += "错误:\n"
            for error in validation_result['errors']:
                info_text += f"  • {error}\n"
            info_text += "\n"

        # 显示警告信息
        if validation_result['warnings']:
            info_text += "警告:\n"
            for warning in validation_result['warnings']:
                info_text += f"  • {warning}\n"
            info_text += "\n"

        # 显示统计信息
        if validation_result['info']:
            info_text += "统计信息:\n"
            for key, value in validation_result['info'].items():
                if key.endswith('_images') or key.endswith('_labels'):
                    info_text += f"  • {key}: {value}\n"

        self.dataset_info_text.setText(info_text)

        # 如果数据集有效，尝试创建配置文件
        if validation_result['valid']:
            self.create_data_config(dataset_path, validation_result)

    def create_data_config(self, dataset_path, validation_result):
        """创建数据配置文件"""
        try:
            # 检查是否已有配置文件
            data_yaml = os.path.join(dataset_path, 'data.yaml')

            if os.path.exists(data_yaml):
                self.current_data_config = data_yaml
                self.add_log(f"✓ 使用现有配置文件: {data_yaml}")
                return

            # 尝试从现有标签文件推断类别
            class_names = self.infer_class_names(dataset_path)

            if class_names:
                # 创建配置文件
                config_file = self.trainer.create_dataset_config(
                    dataset_path,
                    class_names
                )

                if config_file:
                    self.current_data_config = config_file
                    self.add_log(f"✓ 创建配置文件: {config_file}")
                    self.add_log(f"  检测到 {len(class_names)} 个类别: {class_names}")
                else:
                    self.add_log("✗ 配置文件创建失败")
            else:
                self.add_log("⚠ 无法推断类别信息，请手动创建data.yaml文件")

        except Exception as e:
            self.add_log(f"✗ 配置文件创建失败: {str(e)}")

    def infer_class_names(self, dataset_path):
        """从标签文件推断类别名称"""
        try:
            class_ids = set()

            # 检查训练集标签
            train_labels_dir = os.path.join(dataset_path, 'train', 'labels')
            if os.path.exists(train_labels_dir):
                for label_file in os.listdir(train_labels_dir):
                    if label_file.endswith('.txt'):
                        label_path = os.path.join(train_labels_dir, label_file)
                        with open(label_path, 'r') as f:
                            for line in f:
                                parts = line.strip().split()
                                if parts:
                                    class_ids.add(int(parts[0]))

            # 生成类别名称
            if class_ids:
                max_id = max(class_ids)
                class_names = [f"class_{i}" for i in range(max_id + 1)]
                return class_names

            return None

        except Exception as e:
            print(f"推断类别名称失败: {str(e)}")
            return None

    def get_training_config(self):
        """获取当前训练配置"""
        config = {
            'epochs': self.epochs_spin.value(),
            'batch': self.batch_spin.value(),
            'imgsz': self.imgsz_spin.value(),
            'lr0': self.lr0_spin.value(),
            'lrf': self.lrf_spin.value(),
            'momentum': self.momentum_spin.value(),
            'weight_decay': self.weight_decay_spin.value(),
            'warmup_epochs': self.warmup_epochs_spin.value(),
            'workers': self.workers_spin.value(),
            'optimizer': self.optimizer_combo.currentText(),
            'device': self.device_combo.currentText(),
            'amp': self.amp_check.isChecked(),
            'cache': self.cache_check.isChecked(),
            'plots': self.plots_check.isChecked(),
        }

        return config

    def start_training(self):
        """开始训练"""
        # 检查模型
        if self.trainer.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return

        # 检查数据集
        if not self.current_data_config:
            QMessageBox.warning(self, "警告", "请先验证数据集")
            return

        # 设置训练配置
        config = self.get_training_config()
        self.trainer.set_training_config(config)

        # 开始训练
        self.start_training_btn.setEnabled(False)
        self.stop_training_btn.setEnabled(True)
        self.force_stop_btn.setVisible(True)
        self.validate_model_btn.setEnabled(False)

        self.progress_bar.setValue(0)
        self.progress_label.setText("正在训练...")

        self.add_log("=" * 50)
        self.add_log("开始训练模型")
        self.add_log(f"模型: {self.trainer.model_path}")
        self.add_log(f"数据集: {self.current_data_config}")
        self.add_log(f"训练配置: {config}")
        self.add_log("=" * 50)

        # 启动训练线程
        self.training_thread = TrainingThread(self.trainer, self.current_data_config)
        self.training_thread.progressUpdate.connect(self.on_training_progress)
        self.training_thread.logUpdate.connect(self.add_log)
        self.training_thread.finished.connect(self.on_training_finished)

        # 简化的训练启动，不需要进度组件

        self.training_thread.start()

    def stop_training(self):
        """停止训练"""
        if self.training_thread and self.training_thread.isRunning():
            self.add_log("正在停止训练...")

            # 第一步：请求优雅停止
            self.training_thread.stop_training()

            # 第二步：等待一段时间让训练优雅停止
            if not self.training_thread.wait(3000):  # 等待3秒
                self.add_log("优雅停止超时，强制终止训练线程...")

                # 第三步：强制终止线程
                self.training_thread.terminate()

                # 第四步：等待线程真正结束
                if not self.training_thread.wait(2000):  # 再等待2秒
                    self.add_log("警告：训练线程可能仍在运行")
                else:
                    self.add_log("训练线程已强制终止")
            else:
                self.add_log("训练已优雅停止")

        # 重置UI状态
        self.start_training_btn.setEnabled(True)
        self.stop_training_btn.setEnabled(False)
        self.force_stop_btn.setVisible(False)
        self.validate_model_btn.setEnabled(True)

        self.progress_bar.setValue(0)
        self.progress_label.setText("训练已停止")
        self.add_log("=" * 50)
        self.add_log("✗ 训练已停止")
        self.add_log("=" * 50)

        # 简化的停止处理

        # 清理训练线程引用
        self.training_thread = None

    def force_stop_training(self):
        """强制停止训练"""
        reply = QMessageBox.question(
            self, "确认强制停止",
            "强制停止可能导致训练数据丢失，确定要继续吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.add_log("⚠️ 强制停止训练...")

            if self.training_thread and self.training_thread.isRunning():
                # 立即强制终止
                self.training_thread.stop_training()
                self.training_thread.terminate()

                # 等待很短时间
                if not self.training_thread.wait(1000):  # 只等待1秒
                    self.add_log("⚠️ 训练线程可能仍在运行")

                    # 尝试杀死Python进程（极端情况）
                    try:
                        import psutil
                        import os
                        current_process = psutil.Process(os.getpid())
                        for child in current_process.children(recursive=True):
                            if 'python' in child.name().lower():
                                child.terminate()
                        self.add_log("已尝试终止相关Python进程")
                    except:
                        self.add_log("无法终止相关进程")
                else:
                    self.add_log("训练线程已强制终止")

            # 重置UI状态
            self.start_training_btn.setEnabled(True)
            self.stop_training_btn.setEnabled(False)
            self.force_stop_btn.setVisible(False)
            self.validate_model_btn.setEnabled(True)

            self.progress_bar.setValue(0)
            self.progress_label.setText("训练已强制停止")
            self.add_log("=" * 50)
            self.add_log("⚠️ 训练已强制停止")
            self.add_log("=" * 50)

            # 简化的强制停止处理

            # 清理训练线程引用
            self.training_thread = None

    def on_training_progress(self, progress, message):
        """训练进度更新"""
        if progress >= 0:
            self.progress_bar.setValue(progress)
        self.progress_label.setText(message)



    def on_training_finished(self, success, message):
        """训练完成"""
        self.start_training_btn.setEnabled(True)
        self.stop_training_btn.setEnabled(False)
        self.force_stop_btn.setVisible(False)
        self.validate_model_btn.setEnabled(True)

        # 简化的训练完成处理

        if success:
            self.progress_bar.setValue(100)
            self.progress_label.setText("训练完成")
            self.add_log("=" * 50)
            self.add_log("✓ 训练完成!")
            self.add_log("=" * 50)
        else:
            self.progress_bar.setValue(0)
            self.progress_label.setText("训练失败")
            self.add_log("=" * 50)
            self.add_log(f"✗ 训练失败: {message}")
            self.add_log("=" * 50)

    def validate_model(self):
        """验证模型"""
        if self.trainer.model is None:
            QMessageBox.warning(self, "警告", "请先加载模型")
            return

        self.add_log("开始验证模型...")

        # 验证模型
        results = self.trainer.validate_model(self.current_data_config)

        if results:
            self.add_log("✓ 模型验证完成")
            # 这里可以添加更详细的验证结果显示
        else:
            self.add_log("✗ 模型验证失败")

    def update_model_info(self):
        """更新模型信息显示"""
        info = self.trainer.get_model_info()

        if info:
            info_text = ""
            for key, value in info.items():
                if key == 'classes' and isinstance(value, dict):
                    info_text += f"{key}: {len(value)} 个类别\n"
                    # 显示前几个类别
                    class_list = list(value.values())[:5]
                    info_text += f"  前5个类别: {class_list}\n"
                else:
                    info_text += f"{key}: {value}\n"

            self.model_info_text.setText(info_text)
        else:
            self.model_info_text.setText("无法获取模型信息")

    def update_training_buttons(self):
        """更新训练按钮状态"""
        model_loaded = self.trainer.model is not None
        dataset_valid = self.current_dataset is not None

        self.start_training_btn.setEnabled(model_loaded and dataset_valid)
        self.validate_model_btn.setEnabled(model_loaded)

    def add_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)



    def clear_log(self):
        """清空日志"""
        self.log_text.clear()

    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存日志",
            "training_log.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "成功", f"日志已保存到: {file_path}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存日志失败: {str(e)}")

    def load_training_config(self):
        """加载训练配置"""
        current_config = self.get_training_config()

        dialog = TrainingConfigManager(self)
        dialog.configSelected.connect(self.apply_training_config)

        # 显示当前配置
        if current_config:
            import json
            dialog.config_name_edit.setText("当前配置")
            dialog.config_desc_edit.setText("当前使用的训练配置")
            formatted_config = json.dumps(current_config, indent=2, ensure_ascii=False)
            dialog.config_content_edit.setText(formatted_config)

        dialog.exec()

    def save_training_config(self):
        """保存当前训练配置"""
        current_config = self.get_training_config()

        dialog = TrainingConfigManager(self)

        # 预填充当前配置
        import json
        dialog.config_name_edit.setText("新配置")
        dialog.config_desc_edit.setText("自定义训练配置")
        formatted_config = json.dumps(current_config, indent=2, ensure_ascii=False)
        dialog.config_content_edit.setText(formatted_config)

        dialog.exec()

    def reset_training_config(self):
        """重置为默认配置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置为默认训练配置吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 重置所有参数为默认值
            self.epochs_spin.setValue(100)
            self.batch_spin.setValue(16)
            self.imgsz_spin.setValue(640)
            self.lr0_spin.setValue(0.01)
            self.lrf_spin.setValue(0.01)
            self.momentum_spin.setValue(0.937)
            self.weight_decay_spin.setValue(0.0005)
            self.warmup_epochs_spin.setValue(3.0)
            self.workers_spin.setValue(8)
            self.optimizer_combo.setCurrentText('auto')
            self.device_combo.setCurrentText('auto')
            self.amp_check.setChecked(True)
            self.cache_check.setChecked(False)
            self.plots_check.setChecked(True)

            self.add_log("✓ 训练配置已重置为默认值")

    def apply_training_config(self, config):
        """应用训练配置"""
        try:
            # 应用基础参数
            if 'epochs' in config:
                self.epochs_spin.setValue(config['epochs'])
            if 'batch' in config:
                self.batch_spin.setValue(config['batch'])
            if 'imgsz' in config:
                self.imgsz_spin.setValue(config['imgsz'])
            if 'device' in config:
                self.device_combo.setCurrentText(str(config['device']))

            # 应用学习率参数
            if 'lr0' in config:
                self.lr0_spin.setValue(config['lr0'])
            if 'lrf' in config:
                self.lrf_spin.setValue(config['lrf'])
            if 'momentum' in config:
                self.momentum_spin.setValue(config['momentum'])
            if 'weight_decay' in config:
                self.weight_decay_spin.setValue(config['weight_decay'])

            # 应用高级参数
            if 'warmup_epochs' in config:
                self.warmup_epochs_spin.setValue(config['warmup_epochs'])
            if 'workers' in config:
                self.workers_spin.setValue(config['workers'])
            if 'optimizer' in config:
                self.optimizer_combo.setCurrentText(str(config['optimizer']))

            # 应用布尔参数
            if 'amp' in config:
                self.amp_check.setChecked(config['amp'])
            if 'cache' in config:
                self.cache_check.setChecked(config['cache'])
            if 'plots' in config:
                self.plots_check.setChecked(config['plots'])

            self.add_log("✓ 训练配置已应用")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"应用配置失败: {str(e)}")
            self.add_log(f"✗ 配置应用失败: {str(e)}")
