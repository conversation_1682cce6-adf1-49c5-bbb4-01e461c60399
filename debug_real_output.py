#!/usr/bin/env python3
"""
调试实际YOLO输出
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def debug_real_output():
    """调试实际YOLO输出"""
    print("=" * 60)
    print("调试实际YOLO输出")
    print("=" * 60)
    
    # 捕获的所有原始输出
    captured_outputs = []
    
    def capture_output(text):
        """捕获原始输出"""
        captured_outputs.append(text)
        print(f"[RAW] {repr(text)}")
    
    try:
        from ui.widgets.training_widget import TerminalCapture
        
        # 创建终端捕获器
        terminal_capture = TerminalCapture(capture_output)
        
        print("开始捕获实际YOLO输出...")
        
        # 开始捕获
        terminal_capture.start_capture()
        
        try:
            # 模拟YOLO的实际输出方式
            print("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
            print("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
            
            # 模拟表头输出
            header = "      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size"
            print(header)
            
            # 模拟进度输出（YOLO可能使用\r进行覆盖）
            import time
            base_line = "      1/100     0.975G      1.497      3.261      4.773      1.351         45        640: "
            
            # 模拟YOLO的实际输出方式
            for i in range(0, 101, 20):
                progress_bar = f"{i:3d}%|{'#' * (i//10)}{'.' * (10-i//10)}| {i}/96 [00:24<00:17,  2.32it/s]"
                full_line = base_line + progress_bar
                
                # 使用\r进行覆盖（YOLO的实际方式）
                if i > 0:
                    sys.stdout.write('\r')
                sys.stdout.write(full_line)
                sys.stdout.flush()
                time.sleep(0.1)
            
            # 最后输出换行
            print()
            
        finally:
            # 停止捕获
            terminal_capture.stop_capture()
        
        print("\n" + "=" * 60)
        print("捕获的原始输出分析")
        print("=" * 60)
        
        print(f"总共捕获了 {len(captured_outputs)} 个输出片段:")
        for i, output in enumerate(captured_outputs):
            print(f"{i+1:2d}. {repr(output)}")
        
        # 分析输出
        header_found = False
        progress_found = False
        
        for output in captured_outputs:
            if 'Epoch    GPU_mem   box_loss' in output:
                header_found = True
                print(f"\n✅ 找到表头: {repr(output)}")
            
            if '%|' in output and 'it/s' in output:
                progress_found = True
                print(f"✅ 找到进度: {repr(output)}")
        
        print(f"\n分析结果:")
        print(f"表头存在: {'是' if header_found else '否'}")
        print(f"进度存在: {'是' if progress_found else '否'}")
        
        if not header_found:
            print("\n❌ 问题：表头在原始输出中就没有被正确捕获！")
            print("可能的原因：")
            print("1. YOLO使用了特殊的输出方式")
            print("2. 表头和进度在同一个输出片段中")
            print("3. 终端捕获器没有正确处理某些输出")
        
        return header_found and progress_found
        
    except Exception as e:
        print(f"调试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_yolo_style_output():
    """测试YOLO风格的输出"""
    print("\n" + "=" * 60)
    print("测试YOLO风格的输出")
    print("=" * 60)
    
    # 模拟YOLO的实际输出序列
    print("模拟YOLO的实际输出方式...")
    
    # 基本信息
    print("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
    print("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
    
    # 表头
    print("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
    
    # 进度行（使用\r覆盖）
    import time
    base_line = "      1/100     0.975G      1.497      3.261      4.773      1.351         45        640: "
    
    print("开始进度更新...")
    for i in range(0, 101, 25):
        progress_bar = f"{i:3d}%|{'#' * (i//10)}{'.' * (10-i//10)}| {i}/96 [00:24<00:17,  2.32it/s]"
        full_line = base_line + progress_bar
        
        if i > 0:
            # 使用\r回到行首
            print(f"\r{full_line}", end='', flush=True)
        else:
            print(full_line, end='', flush=True)
        
        time.sleep(0.2)
    
    print()  # 最后的换行
    print("进度更新完成")

if __name__ == "__main__":
    print("开始调试实际YOLO输出...")
    
    # 测试YOLO风格输出
    test_yolo_style_output()
    
    # 调试实际输出捕获
    success = debug_real_output()
    
    print("\n" + "=" * 60)
    print("调试总结")
    print("=" * 60)
    
    if success:
        print("✅ 输出捕获正常")
    else:
        print("❌ 输出捕获存在问题")
        print("需要改进终端捕获逻辑")
    
    print("=" * 60)
