#!/usr/bin/env python3
"""
测试动态更新功能
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_dynamic_update():
    """测试动态更新功能"""
    print("=" * 60)
    print("测试动态更新功能")
    print("=" * 60)
    
    # 捕获的日志
    static_logs = []
    dynamic_logs = []
    
    def static_log_capture(message):
        """静态日志捕获（新行）"""
        static_logs.append(message)
        sys.__stdout__.write(f"[STATIC] {message}\n")
        sys.__stdout__.flush()
    
    def dynamic_log_capture(message):
        """动态日志捕获（覆盖最后一行）"""
        dynamic_logs.append(message)
        sys.__stdout__.write(f"\r[DYNAMIC] {message}")
        sys.__stdout__.flush()
    
    # 创建终端捕获器
    from ui.widgets.training_widget import TerminalCapture
    terminal_capture = TerminalCapture(static_log_capture, dynamic_log_capture)
    
    print("开始测试动态更新功能...")
    
    try:
        # 开始捕获
        terminal_capture.start_capture()
        
        # 测试静态输出（换行符结尾）
        print("这是静态行1")
        print("这是静态行2")
        
        # 测试动态输出（回车符更新）
        print("开始动态更新测试:")
        for i in range(5):
            print(f"\r进度: {i*20}%|{'█'*i}{'▌'*(5-i)}| {i}/5", end='', flush=True)
            time.sleep(0.5)
        print()  # 换行，将动态行变为静态行
        
        # 模拟YOLO训练输出
        print("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 模拟动态更新的训练进度
        for progress in [10, 25, 50, 75, 100]:
            epoch_line = f"      2/100      1.05G      1.417      2.508      4.337       1.21         19        640: {progress}%|{'#'*(progress//10)}{'.'*(10-progress//10)}| 96/96 [00:40<00:00,  2.35it/s]"
            print(f"\r{epoch_line}", end='', flush=True)
            time.sleep(0.8)
        print()  # 换行
        
        # 验证结果
        print("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:01<00:00,  2.99it/s]")
        print("                   all         24        196     0.0496      0.503      0.106      0.076     0.0462      0.427     0.0961     0.0534")
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试异常: {e}")
        
    finally:
        # 停止捕获
        terminal_capture.stop_capture()
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("动态更新测试结果:")
    print(f"静态日志条数: {len(static_logs)}")
    print(f"动态日志条数: {len(dynamic_logs)}")
    
    # 显示静态日志
    print("\n静态日志内容:")
    for i, log in enumerate(static_logs):
        print(f"  {i+1:2d}. {log}")
    
    # 显示动态日志
    print("\n动态日志内容:")
    for i, log in enumerate(dynamic_logs):
        print(f"  {i+1:2d}. {log}")
    
    # 检查关键功能
    has_static = len(static_logs) > 0
    has_dynamic = len(dynamic_logs) > 0
    has_progress = any('进度:' in log for log in dynamic_logs)
    has_epoch = any('2/100' in log for log in dynamic_logs)
    
    print(f"\n功能验证:")
    print(f"✓ 静态日志捕获: {'成功' if has_static else '失败'}")
    print(f"✓ 动态日志捕获: {'成功' if has_dynamic else '失败'}")
    print(f"✓ 进度条更新: {'成功' if has_progress else '失败'}")
    print(f"✓ YOLO格式输出: {'成功' if has_epoch else '失败'}")
    
    print("=" * 60)
    print("动态更新功能测试完成")
    print("期望效果:")
    print("- 静态行：每次都创建新行")
    print("- 动态行：覆盖最后一行，实现进度条效果")
    print("- YOLO训练：进度条动态刷新，完成后变为静态行")
    print("=" * 60)
    
    return has_static and has_dynamic and has_progress

if __name__ == "__main__":
    success = test_dynamic_update()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    
    if success:
        print("\n✅ 动态更新功能工作正常！")
        print("现在YOLO训练的进度条将会动态刷新，而不是创建新行。")
        print("您将看到类似这样的效果：")
        print("      2/100      1.05G      1.417      2.508      4.337       1.21         19        640: 100%|##########| 96/96 [00:40<00:00,  2.35it/s]")
        print("进度条会在同一行动态更新，直到完成。")
    else:
        print("\n❌ 动态更新功能存在问题，需要进一步调试。")
