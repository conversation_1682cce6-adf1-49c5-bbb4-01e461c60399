#!/usr/bin/env python3
"""
测试YOLO格式化显示
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_yolo_format():
    """测试YOLO格式化显示"""
    print("=" * 60)
    print("测试YOLO格式化显示")
    print("=" * 60)
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.widgets.training_widget import TrainingWidget
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建训练组件
        widget = TrainingWidget()
        
        # 模拟完整的YOLO训练输出序列
        print("开始模拟YOLO训练输出...")
        
        # 基本信息
        widget.add_log("Ultralytics YOLOv8.0.0 🚀 Python-3.8.10 torch-1.12.0 CPU")
        widget.add_log("Model summary: 225 layers, 11156544 parameters, 0 gradients, 28.6 GFLOPs")
        print("✓ 添加了基本信息")
        
        # 第1个epoch
        print("\n模拟第1个epoch...")
        widget.add_log("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 训练进度（动态更新）
        base_line = "      1/100      1.05G      1.523      3.141      4.765      1.325         22        640: "
        for progress in [0, 25, 50, 75, 100]:
            progress_bar = f"{progress:3d}%|{'#' * (progress//10)}{'.' * (10-progress//10)}| {progress}/96 [00:39<00:00,  2.40it/s]"
            widget.add_log(base_line + progress_bar)
            time.sleep(0.1)
        
        # 验证阶段
        widget.add_log("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:00<00:00,  3.20it/s]")
        widget.add_log("                   all         24        196     0.0662     0.0864     0.0428     0.0302     0.0116     0.0451    0.00739    0.00122")
        print("✓ 完成第1个epoch")
        
        # 第2个epoch
        print("\n模拟第2个epoch...")
        widget.add_log("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 训练进度（动态更新）
        base_line = "      2/100      1.05G      1.417      2.508      4.337       1.21         19        640: "
        for progress in [0, 50, 100]:
            progress_bar = f"{progress:3d}%|{'#' * (progress//10)}{'.' * (10-progress//10)}| {progress}/96 [00:38<00:00,  2.49it/s]"
            widget.add_log(base_line + progress_bar)
            time.sleep(0.1)
        
        # 验证阶段
        widget.add_log("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:01<00:00,  2.93it/s]")
        widget.add_log("                   all         24        196     0.0496      0.503      0.106      0.076     0.0462      0.427     0.0961     0.0534")
        print("✓ 完成第2个epoch")
        
        # 第3个epoch
        print("\n模拟第3个epoch...")
        widget.add_log("      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size")
        
        # 训练进度（动态更新）
        base_line = "      3/100       1.2G      1.294      2.254      3.832      1.143         15        640: "
        for progress in [0, 100]:
            progress_bar = f"{progress:3d}%|{'#' * (progress//10)}{'.' * (10-progress//10)}| {progress}/96 [00:39<00:00,  2.44it/s]"
            widget.add_log(base_line + progress_bar)
            time.sleep(0.1)
        
        # 验证阶段
        widget.add_log("                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P          R      mAP50  mAP50-95): 100%|##########| 3/3 [00:01<00:00,  2.76it/s]")
        widget.add_log("                   all         24        196      0.231      0.327      0.138      0.102      0.243      0.247      0.124     0.0783")
        print("✓ 完成第3个epoch")
        
        # 检查最终结果
        print("\n" + "=" * 60)
        print("最终结果分析")
        print("=" * 60)
        
        final_text = widget.log_text.toPlainText()
        lines = final_text.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        
        print(f"最终日志行数: {len(non_empty_lines)}")
        print("完整日志内容:")
        for i, line in enumerate(non_empty_lines):
            print(f"  {i+1:2d}. {line}")
        
        # 验证格式
        expected_pattern = [
            "Ultralytics YOLOv8",  # 基本信息
            "Model summary",       # 模型摘要
            "",                    # 空行
            "Epoch    GPU_mem",     # 第1个epoch表头
            "1/100",               # 第1个epoch训练进度
            "Class     Images",    # 第1个epoch验证进度
            "all         24",      # 第1个epoch验证结果
            "",                    # 空行
            "Epoch    GPU_mem",     # 第2个epoch表头
            "2/100",               # 第2个epoch训练进度
            "Class     Images",    # 第2个epoch验证进度
            "all         24",      # 第2个epoch验证结果
            "",                    # 空行
            "Epoch    GPU_mem",     # 第3个epoch表头
            "3/100",               # 第3个epoch训练进度
            "Class     Images",    # 第3个epoch验证进度
            "all         24",      # 第3个epoch验证结果
        ]
        
        # 检查关键内容
        has_basic_info = any('Ultralytics YOLOv8' in line for line in non_empty_lines)
        has_model_summary = any('Model summary' in line for line in non_empty_lines)
        
        # 检查每个epoch的完整性
        epoch_headers = [line for line in non_empty_lines if 'Epoch    GPU_mem' in line]
        epoch_1_progress = [line for line in non_empty_lines if '1/100' in line and '%|' in line]
        epoch_2_progress = [line for line in non_empty_lines if '2/100' in line and '%|' in line]
        epoch_3_progress = [line for line in non_empty_lines if '3/100' in line and '%|' in line]
        
        validation_progress = [line for line in non_empty_lines if 'Class     Images' in line and '%|' in line]
        validation_results = [line for line in non_empty_lines if 'all         24' in line]
        
        print(f"\n格式验证:")
        print(f"✓ 基本信息: {'是' if has_basic_info else '否'}")
        print(f"✓ 模型摘要: {'是' if has_model_summary else '否'}")
        print(f"✓ Epoch表头数量: {len(epoch_headers)} (期望: 3)")
        print(f"✓ Epoch1训练进度: {len(epoch_1_progress)} (期望: 1)")
        print(f"✓ Epoch2训练进度: {len(epoch_2_progress)} (期望: 1)")
        print(f"✓ Epoch3训练进度: {len(epoch_3_progress)} (期望: 1)")
        print(f"✓ 验证进度行数: {len(validation_progress)} (期望: 3)")
        print(f"✓ 验证结果行数: {len(validation_results)} (期望: 3)")
        
        # 总体评估
        format_correct = (
            has_basic_info and has_model_summary and
            len(epoch_headers) == 3 and
            len(epoch_1_progress) == 1 and
            len(epoch_2_progress) == 1 and
            len(epoch_3_progress) == 1 and
            len(validation_progress) == 3 and
            len(validation_results) == 3
        )
        
        print(f"\n测试结果: {'✅ 成功' if format_correct else '❌ 失败'}")
        
        if format_correct:
            print("🎉 YOLO格式化显示完美实现！")
            print("现在训练日志将按照标准YOLO格式显示：")
            print("- 每个epoch显示为完整的块")
            print("- 包含表头、训练进度、验证进度、验证结果")
            print("- 进度行动态更新，不重复")
            print("- epoch之间有空行分隔")
        else:
            print("⚠️ 格式化显示需要进一步调试。")
        
        return format_correct
        
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试YOLO格式化显示...")
    
    success = test_yolo_format()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 YOLO格式化显示测试通过！")
        print("现在可以重新启动训练，将看到完美的YOLO格式输出！")
    else:
        print("❌ 测试失败，需要进一步调试。")
    
    print("=" * 60)
