#!/usr/bin/env python3
"""
浏览器下载助手
帮助用户使用浏览器下载YOLO模型文件
"""

import os
import webbrowser
import time
from pathlib import Path

def open_download_links():
    """在浏览器中打开模型下载链接"""
    
    models = {
        'YOLOv8n (推荐)': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n.pt',
        'YOLOv8s': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s.pt',
        'YOLOv8m': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8m.pt',
        'YOLOv8n-seg (分割)': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n-seg.pt',
        'YOLOv8s-seg (分割)': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8s-seg.pt',
        'YOLOv8n-pose (姿态)': 'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n-pose.pt',
    }
    
    print("🌐 YOLO模型浏览器下载助手")
    print("=" * 50)
    print("即将在浏览器中打开模型下载链接...")
    print("请在浏览器中下载需要的模型文件。")
    print()
    
    # 创建models目录
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    print(f"✓ 已创建模型目录: {models_dir.absolute()}")
    
    print("\n可下载的模型:")
    for i, (name, url) in enumerate(models.items(), 1):
        print(f"{i}. {name}")
    
    print("\n选择要下载的模型 (输入数字，多个用逗号分隔，或输入'all'下载全部):")
    choice = input("请选择: ").strip()
    
    selected_models = []
    
    if choice.lower() == 'all':
        selected_models = list(models.items())
    else:
        try:
            indices = [int(x.strip()) for x in choice.split(',')]
            model_list = list(models.items())
            selected_models = [model_list[i-1] for i in indices if 1 <= i <= len(model_list)]
        except (ValueError, IndexError):
            print("❌ 输入无效，将下载推荐模型 YOLOv8n")
            selected_models = [('YOLOv8n (推荐)', models['YOLOv8n (推荐)'])]
    
    print(f"\n🚀 即将打开 {len(selected_models)} 个下载链接...")
    
    for i, (name, url) in enumerate(selected_models):
        print(f"\n{i+1}. 打开 {name} 下载链接...")
        webbrowser.open(url)
        
        if i < len(selected_models) - 1:  # 不是最后一个
            print("   等待3秒后打开下一个链接...")
            time.sleep(3)
    
    print(f"\n✅ 已在浏览器中打开所有下载链接！")
    print("\n📋 下载完成后的操作:")
    print("1. 将下载的 .pt 文件移动到 'models' 目录")
    print("2. 运行 'python main.py' 启动程序")
    print("3. 在训练界面中选择'加载自定义模型'")
    print("4. 浏览并选择下载的模型文件")

def check_downloads():
    """检查已下载的模型文件"""
    print("\n🔍 检查已下载的模型文件...")
    
    models_dir = Path("models")
    downloads_dir = Path.home() / "Downloads"
    
    # 检查models目录
    models_files = list(models_dir.glob("*.pt"))
    if models_files:
        print(f"\n✅ models目录中找到 {len(models_files)} 个模型文件:")
        for file in models_files:
            size_mb = file.stat().st_size / (1024 * 1024)
            print(f"   • {file.name} ({size_mb:.1f} MB)")
    else:
        print(f"\n📁 models目录为空: {models_dir.absolute()}")
    
    # 检查Downloads目录
    download_files = list(downloads_dir.glob("yolo*.pt"))
    if download_files:
        print(f"\n📥 Downloads目录中找到 {len(download_files)} 个YOLO模型文件:")
        for file in download_files:
            size_mb = file.stat().st_size / (1024 * 1024)
            print(f"   • {file.name} ({size_mb:.1f} MB)")
        
        # 询问是否移动文件
        if input("\n是否将Downloads中的模型文件移动到models目录? (y/n): ").lower() == 'y':
            move_files_from_downloads()
    else:
        print(f"\n📁 Downloads目录中未找到YOLO模型文件")

def move_files_from_downloads():
    """从Downloads目录移动模型文件到models目录"""
    downloads_dir = Path.home() / "Downloads"
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    download_files = list(downloads_dir.glob("yolo*.pt"))
    
    for file in download_files:
        target_path = models_dir / file.name
        
        if target_path.exists():
            print(f"⚠️  {file.name} 已存在，跳过")
            continue
        
        try:
            import shutil
            shutil.move(str(file), str(target_path))
            print(f"✅ 已移动: {file.name} -> models/")
        except Exception as e:
            print(f"❌ 移动失败 {file.name}: {e}")
    
    print("\n🎉 文件移动完成！")

def verify_models():
    """验证模型文件是否可用"""
    print("\n🔬 验证模型文件...")
    
    models_dir = Path("models")
    model_files = list(models_dir.glob("*.pt"))
    
    if not model_files:
        print("❌ 未找到模型文件")
        return
    
    for model_file in model_files:
        try:
            from ultralytics import YOLO
            model = YOLO(str(model_file))
            print(f"✅ {model_file.name} - 验证成功")
            
            # 显示模型信息
            if hasattr(model, 'model') and model.model:
                task = getattr(model, 'task', 'unknown')
                print(f"   任务类型: {task}")
                if hasattr(model, 'names'):
                    print(f"   类别数量: {len(model.names)}")
            
        except Exception as e:
            print(f"❌ {model_file.name} - 验证失败: {e}")

def main():
    """主函数"""
    print("🎯 YOLO模型浏览器下载助手")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 在浏览器中打开下载链接")
        print("2. 检查已下载的文件")
        print("3. 从Downloads移动文件到models目录")
        print("4. 验证模型文件")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == '1':
            open_download_links()
        elif choice == '2':
            check_downloads()
        elif choice == '3':
            move_files_from_downloads()
        elif choice == '4':
            verify_models()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选项，请重新选择")

if __name__ == "__main__":
    main()
